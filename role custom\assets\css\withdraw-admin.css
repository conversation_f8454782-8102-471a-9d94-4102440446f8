/**
 * Role Custom Para Çekme Admin CSS
 * 
 * @package Role_Custom
 * @subpackage Assets
 * @since 1.4.0
 */

/* Genel Stil */
.role-custom-withdraws {
    margin: 20px 0;
}

/* İstatistik Kartları */
.role-custom-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.role-custom-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.role-custom-stat-card .stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #2271b1;
    margin-bottom: 5px;
}

.role-custom-stat-card .stat-label {
    color: #646970;
    font-size: 14px;
}

/* Tab Navigasyon */
.nav-tab-wrapper {
    margin: 20px 0 10px 0;
}

.nav-tab .count {
    background: #646970;
    color: #fff;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    margin-left: 5px;
}

.nav-tab-active .count {
    background: #2271b1;
}

/* Tablo Stilleri */
.role-custom-withdraws-table {
    margin-top: 20px;
}

.role-custom-withdraws-table table {
    border-collapse: collapse;
}

.role-custom-withdraws-table th,
.role-custom-withdraws-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #c3c4c7;
}

.role-custom-withdraws-table th {
    background: #f6f7f7;
    font-weight: 600;
}

/* Durum Etiketleri */
.status-pending {
    color: #d63638;
    font-weight: 600;
}

.status-approved {
    color: #00a32a;
    font-weight: 600;
}

.status-cancelled {
    color: #646970;
    font-weight: 600;
}

.status-processing {
    color: #dba617;
    font-weight: 600;
}

/* Boş Durum */
.role-custom-empty-state {
    text-align: center;
    padding: 40px 20px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.role-custom-empty-state p {
    color: #646970;
    font-size: 16px;
    margin: 0;
}

/* Modal Stilleri */
.role-custom-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.role-custom-modal-content {
    background-color: #fff;
    margin: 5% auto;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.role-custom-modal-header {
    padding: 20px;
    border-bottom: 1px solid #c3c4c7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f6f7f7;
}

.role-custom-modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #1d2327;
}

.role-custom-modal-close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #646970;
    line-height: 1;
}

.role-custom-modal-close:hover {
    color: #d63638;
}

.role-custom-modal-body {
    padding: 20px;
}

.role-custom-modal-footer {
    padding: 20px;
    border-top: 1px solid #c3c4c7;
    text-align: right;
    background: #f6f7f7;
}

.role-custom-modal-footer .button {
    margin-left: 10px;
}

/* Form Stilleri */
.form-field {
    margin-bottom: 20px;
}

.form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #1d2327;
}

.form-field textarea,
.form-field input[type="text"],
.form-field input[type="email"],
.form-field select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 14px;
}

.form-field textarea:focus,
.form-field input:focus,
.form-field select:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.form-field .description {
    margin-top: 5px;
    color: #646970;
    font-size: 13px;
    font-style: italic;
}

/* Detay Görünümü */
.withdraw-detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.withdraw-detail-section {
    background: #f6f7f7;
    padding: 15px;
    border-radius: 4px;
}

.withdraw-detail-section h4 {
    margin: 0 0 10px 0;
    color: #1d2327;
    font-size: 16px;
}

.withdraw-detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dcdcde;
}

.withdraw-detail-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.withdraw-detail-label {
    font-weight: 600;
    color: #646970;
}

.withdraw-detail-value {
    color: #1d2327;
}

/* Ödeme Detayları */
.payment-details {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.payment-details h4 {
    margin: 0 0 10px 0;
    color: #1d2327;
}

.payment-details .detail-item {
    margin-bottom: 8px;
}

.payment-details .detail-label {
    font-weight: 600;
    color: #646970;
    display: inline-block;
    width: 120px;
}

/* Responsive */
@media (max-width: 768px) {
    .role-custom-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .withdraw-detail-grid {
        grid-template-columns: 1fr;
    }
    
    .role-custom-modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    .role-custom-withdraws-table {
        overflow-x: auto;
    }
}

/* Loading Durumu */
.role-custom-loading {
    text-align: center;
    padding: 20px;
}

.role-custom-loading::after {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Başarı/Hata Mesajları */
.role-custom-notice {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid;
    border-radius: 4px;
}

.role-custom-notice.notice-success {
    background: #f0f8ff;
    border-left-color: #00a32a;
    color: #00a32a;
}

.role-custom-notice.notice-error {
    background: #fef7f7;
    border-left-color: #d63638;
    color: #d63638;
}

.role-custom-notice.notice-warning {
    background: #fcf9e8;
    border-left-color: #dba617;
    color: #dba617;
}
