<?php
/**
 * Role Custom Para Çekme Güvenlik Sınıfı
 * 
 * @package Role_Custom
 * @subpackage Withdraw
 * @since 1.4.0
 */

// Dogrudan erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Para çekme sistemi guvenlik kontrollerini yoneten sinif
 */
class Role_Custom_Withdraw_Security {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Guvenlik ayarlari
     */
    private $security_settings;
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->load_security_settings();
        $this->init_hooks();
    }
    
    /**
     * Guvenlik ayarlarini yukle
     */
    private function load_security_settings() {
        $this->security_settings = get_option('role_custom_withdraw_security_settings', [
            'max_daily_requests' => 3,
            'max_weekly_amount' => 5000,
            'min_account_age_days' => 30,
            'require_phone_verification' => false,
            'require_identity_verification' => false,
            'suspicious_activity_threshold' => 5,
            'ip_whitelist_enabled' => false,
            'ip_whitelist' => [],
            'blocked_ips' => [],
            'rate_limit_enabled' => true,
            'rate_limit_requests' => 10,
            'rate_limit_window' => 3600 // 1 saat
        ]);
    }
    
    /**
     * Hook'lari baslat
     */
    private function init_hooks() {
        // Para çekme oncesi guvenlik kontrolleri
        add_filter('role_custom_before_withdraw_request', [$this, 'validate_withdraw_security'], 10, 3);
        
        // Supheli aktivite takibi
        add_action('role_custom_withdraw_request_created', [$this, 'log_withdraw_activity'], 10, 2);
        add_action('role_custom_withdraw_request_failed', [$this, 'log_failed_attempt'], 10, 3);
        
        // IP kontrolu
        add_filter('role_custom_validate_user_ip', [$this, 'validate_user_ip'], 10, 2);
        
        // Rate limiting
        add_filter('role_custom_check_rate_limit', [$this, 'check_rate_limit'], 10, 2);
        
        // Admin hook'lari
        add_action('wp_ajax_role_custom_update_security_settings', [$this, 'ajax_update_security_settings']);
        add_action('wp_ajax_role_custom_block_ip', [$this, 'ajax_block_ip']);
        add_action('wp_ajax_role_custom_unblock_ip', [$this, 'ajax_unblock_ip']);
    }
    
    /**
     * Para çekme guvenlik dogrulamasi
     */
    public function validate_withdraw_security($is_valid, $user_id, $amount) {
        if (!$is_valid) {
            return $is_valid;
        }
        
        // IP kontrolu
        if (!$this->validate_user_ip(true, $user_id)) {
            return new WP_Error('blocked_ip', 'IP adresiniz engellenmiş.');
        }
        
        // Rate limiting kontrolu
        if (!$this->check_rate_limit(true, $user_id)) {
            return new WP_Error('rate_limit', 'Çok fazla istek gonderdiniz. Lutfen bekleyin.');
        }
        
        // Gunluk talep limiti
        if (!$this->check_daily_request_limit($user_id)) {
            return new WP_Error('daily_limit', 'Gunluk para çekme talep limitinizi astiniz.');
        }
        
        // Haftalik tutar limiti
        if (!$this->check_weekly_amount_limit($user_id, $amount)) {
            return new WP_Error('weekly_limit', 'Haftalik para çekme tutar limitinizi astiniz.');
        }
        
        // Hesap yasi kontrolu
        if (!$this->check_account_age($user_id)) {
            return new WP_Error('account_age', 'Hesabiniz para çekme icin yeterince eski degil.');
        }
        
        // Telefon dogrulama kontrolu
        if ($this->security_settings['require_phone_verification'] && !$this->is_phone_verified($user_id)) {
            return new WP_Error('phone_verification', 'Telefon numaranizi dogrulamaniz gerekiyor.');
        }
        
        // Kimlik dogrulama kontrolu
        if ($this->security_settings['require_identity_verification'] && !$this->is_identity_verified($user_id)) {
            return new WP_Error('identity_verification', 'Kimlik dogrulamanizi tamamlamaniz gerekiyor.');
        }
        
        // Supheli aktivite kontrolu
        if ($this->has_suspicious_activity($user_id)) {
            return new WP_Error('suspicious_activity', 'Hesabinizda supheli aktivite tespit edildi.');
        }
        
        return true;
    }
    
    /**
     * IP adres dogrulamasi
     */
    public function validate_user_ip($is_valid, $user_id) {
        $user_ip = $this->get_user_ip();
        
        // Engellenen IP'ler kontrolu
        if (in_array($user_ip, $this->security_settings['blocked_ips'])) {
            return false;
        }
        
        // IP whitelist kontrolu
        if ($this->security_settings['ip_whitelist_enabled']) {
            if (!in_array($user_ip, $this->security_settings['ip_whitelist'])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Rate limiting kontrolu
     */
    public function check_rate_limit($is_valid, $user_id) {
        if (!$this->security_settings['rate_limit_enabled']) {
            return true;
        }
        
        $cache_key = 'role_custom_rate_limit_' . $user_id;
        $requests = get_transient($cache_key);
        
        if ($requests === false) {
            $requests = 0;
        }
        
        if ($requests >= $this->security_settings['rate_limit_requests']) {
            return false;
        }
        
        // Istek sayisini artir
        set_transient($cache_key, $requests + 1, $this->security_settings['rate_limit_window']);
        
        return true;
    }
    
    /**
     * Gunluk talep limiti kontrolu
     */
    private function check_daily_request_limit($user_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;
        $today = date('Y-m-d');
        
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} 
             WHERE user_id = %d 
             AND DATE(created_at) = %s",
            $user_id,
            $today
        ));
        
        return $count < $this->security_settings['max_daily_requests'];
    }
    
    /**
     * Haftalik tutar limiti kontrolu
     */
    private function check_weekly_amount_limit($user_id, $amount) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;
        $week_start = date('Y-m-d', strtotime('monday this week'));
        
        $total_amount = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM {$table_name} 
             WHERE user_id = %d 
             AND created_at >= %s 
             AND status IN (%d, %d)",
            $user_id,
            $week_start,
            Role_Custom_Withdraw_Manager::STATUS_PENDING,
            Role_Custom_Withdraw_Manager::STATUS_APPROVED
        ));
        
        $total_amount = floatval($total_amount);
        
        return ($total_amount + $amount) <= $this->security_settings['max_weekly_amount'];
    }
    
    /**
     * Hesap yasi kontrolu
     */
    private function check_account_age($user_id) {
        $user = get_userdata($user_id);
        if (!$user) {
            return false;
        }
        
        $account_age_days = (time() - strtotime($user->user_registered)) / DAY_IN_SECONDS;
        
        return $account_age_days >= $this->security_settings['min_account_age_days'];
    }
    
    /**
     * Telefon dogrulama durumu
     */
    private function is_phone_verified($user_id) {
        // Telefon dogrulama meta'sini kontrol et
        return get_user_meta($user_id, '_role_custom_phone_verified', true) === 'yes';
    }
    
    /**
     * Kimlik dogrulama durumu
     */
    private function is_identity_verified($user_id) {
        // Kimlik dogrulama meta'sini kontrol et
        return get_user_meta($user_id, '_role_custom_identity_verified', true) === 'yes';
    }
    
    /**
     * Supheli aktivite kontrolu
     */
    private function has_suspicious_activity($user_id) {
        $suspicious_count = get_user_meta($user_id, '_role_custom_suspicious_activity_count', true);
        $suspicious_count = intval($suspicious_count);
        
        return $suspicious_count >= $this->security_settings['suspicious_activity_threshold'];
    }
    
    /**
     * Para çekme aktivitesini logla
     */
    public function log_withdraw_activity($withdraw_id, $user_id) {
        $this->log_activity($user_id, 'withdraw_request', [
            'withdraw_id' => $withdraw_id,
            'ip_address' => $this->get_user_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'timestamp' => current_time('mysql')
        ]);
    }
    
    /**
     * Basarisiz girisimleri logla
     */
    public function log_failed_attempt($user_id, $error_code, $error_message) {
        $this->log_activity($user_id, 'withdraw_failed', [
            'error_code' => $error_code,
            'error_message' => $error_message,
            'ip_address' => $this->get_user_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'timestamp' => current_time('mysql')
        ]);
        
        // Supheli aktivite sayacini artir
        $this->increment_suspicious_activity($user_id);
    }
    
    /**
     * Aktivite logla
     */
    private function log_activity($user_id, $activity_type, $data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'role_custom_security_logs';
        
        $wpdb->insert(
            $table_name,
            [
                'user_id' => $user_id,
                'activity_type' => $activity_type,
                'activity_data' => maybe_serialize($data),
                'ip_address' => $this->get_user_ip(),
                'created_at' => current_time('mysql')
            ],
            ['%d', '%s', '%s', '%s', '%s']
        );
    }
    
    /**
     * Supheli aktivite sayacini artir
     */
    private function increment_suspicious_activity($user_id) {
        $count = get_user_meta($user_id, '_role_custom_suspicious_activity_count', true);
        $count = intval($count) + 1;
        
        update_user_meta($user_id, '_role_custom_suspicious_activity_count', $count);
        
        // Esik degerini astiysa admin'e bildir
        if ($count >= $this->security_settings['suspicious_activity_threshold']) {
            $this->notify_admin_suspicious_activity($user_id, $count);
        }
    }
    
    /**
     * Admin'e supheli aktivite bildirimi
     */
    private function notify_admin_suspicious_activity($user_id, $count) {
        $user = get_userdata($user_id);
        $admin_email = get_option('admin_email');
        
        $subject = sprintf('[%s] Supheli Aktivite Tespit Edildi', get_bloginfo('name'));
        $message = sprintf(
            "Kullanici: %s (%s)\nSupheli aktivite sayisi: %d\nIP Adresi: %s\nTarih: %s",
            $user->display_name,
            $user->user_email,
            $count,
            $this->get_user_ip(),
            current_time('mysql')
        );
        
        wp_mail($admin_email, $subject, $message);
    }
    
    /**
     * Kullanicinin IP adresini al
     */
    private function get_user_ip() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'] ?? '';
        }
    }

    /**
     * AJAX: Guvenlik ayarlarini guncelle
     */
    public function ajax_update_security_settings() {
        // Yetki kontrolu
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Yetkisiz erisim.');
        }

        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_admin_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $settings = [
            'max_daily_requests' => intval($_POST['max_daily_requests']),
            'max_weekly_amount' => floatval($_POST['max_weekly_amount']),
            'min_account_age_days' => intval($_POST['min_account_age_days']),
            'require_phone_verification' => isset($_POST['require_phone_verification']),
            'require_identity_verification' => isset($_POST['require_identity_verification']),
            'suspicious_activity_threshold' => intval($_POST['suspicious_activity_threshold']),
            'ip_whitelist_enabled' => isset($_POST['ip_whitelist_enabled']),
            'rate_limit_enabled' => isset($_POST['rate_limit_enabled']),
            'rate_limit_requests' => intval($_POST['rate_limit_requests']),
            'rate_limit_window' => intval($_POST['rate_limit_window'])
        ];

        // IP listelerini isle
        if (isset($_POST['ip_whitelist'])) {
            $settings['ip_whitelist'] = array_filter(array_map('trim', explode("\n", $_POST['ip_whitelist'])));
        }

        if (isset($_POST['blocked_ips'])) {
            $settings['blocked_ips'] = array_filter(array_map('trim', explode("\n", $_POST['blocked_ips'])));
        }

        update_option('role_custom_withdraw_security_settings', $settings);
        $this->security_settings = $settings;

        wp_send_json_success('Guvenlik ayarlari guncellendi.');
    }

    /**
     * AJAX: IP engelle
     */
    public function ajax_block_ip() {
        // Yetki kontrolu
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Yetkisiz erisim.');
        }

        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_admin_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $ip = sanitize_text_field($_POST['ip']);

        if (!filter_var($ip, FILTER_VALIDATE_IP)) {
            wp_send_json_error('Gecersiz IP adresi.');
        }

        if (!in_array($ip, $this->security_settings['blocked_ips'])) {
            $this->security_settings['blocked_ips'][] = $ip;
            update_option('role_custom_withdraw_security_settings', $this->security_settings);
        }

        wp_send_json_success('IP adresi engellendi.');
    }

    /**
     * AJAX: IP engel kaldir
     */
    public function ajax_unblock_ip() {
        // Yetki kontrolu
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Yetkisiz erisim.');
        }

        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_admin_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $ip = sanitize_text_field($_POST['ip']);

        $key = array_search($ip, $this->security_settings['blocked_ips']);
        if ($key !== false) {
            unset($this->security_settings['blocked_ips'][$key]);
            $this->security_settings['blocked_ips'] = array_values($this->security_settings['blocked_ips']);
            update_option('role_custom_withdraw_security_settings', $this->security_settings);
        }

        wp_send_json_success('IP adresi engeli kaldirildi.');
    }

    /**
     * Guvenlik ayarlarini al
     */
    public function get_security_settings() {
        return $this->security_settings;
    }

    /**
     * Guvenlik loglarini al
     */
    public function get_security_logs($limit = 50, $offset = 0, $user_id = null, $activity_type = null) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'role_custom_security_logs';

        $where_conditions = [];
        $params = [];

        if ($user_id) {
            $where_conditions[] = 'user_id = %d';
            $params[] = $user_id;
        }

        if ($activity_type) {
            $where_conditions[] = 'activity_type = %s';
            $params[] = $activity_type;
        }

        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }

        $params[] = $limit;
        $params[] = $offset;

        $query = "SELECT * FROM {$table_name}
                  {$where_clause}
                  ORDER BY created_at DESC
                  LIMIT %d OFFSET %d";

        return $wpdb->get_results($wpdb->prepare($query, $params));
    }

    /**
     * Supheli kullanicilari al
     */
    public function get_suspicious_users() {
        global $wpdb;

        $users_table = $wpdb->users;
        $usermeta_table = $wpdb->usermeta;

        return $wpdb->get_results($wpdb->prepare(
            "SELECT u.ID, u.display_name, u.user_email, um.meta_value as suspicious_count
             FROM {$users_table} u
             INNER JOIN {$usermeta_table} um ON u.ID = um.user_id
             WHERE um.meta_key = '_role_custom_suspicious_activity_count'
             AND CAST(um.meta_value AS UNSIGNED) >= %d
             ORDER BY CAST(um.meta_value AS UNSIGNED) DESC",
            $this->security_settings['suspicious_activity_threshold']
        ));
    }

    /**
     * Kullanicinin supheli aktivite sayacini sifirla
     */
    public function reset_suspicious_activity($user_id) {
        delete_user_meta($user_id, '_role_custom_suspicious_activity_count');

        // Log kaydi
        $this->log_activity($user_id, 'suspicious_activity_reset', [
            'reset_by' => get_current_user_id(),
            'timestamp' => current_time('mysql')
        ]);

        return true;
    }

    /**
     * Guvenlik tablosunu olustur
     */
    public static function create_security_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'role_custom_security_logs';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS `{$table_name}` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint(20) unsigned NOT NULL,
            `activity_type` varchar(50) NOT NULL,
            `activity_data` longtext DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `activity_type` (`activity_type`),
            KEY `ip_address` (`ip_address`),
            KEY `created_at` (`created_at`)
        ) {$charset_collate};";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}
