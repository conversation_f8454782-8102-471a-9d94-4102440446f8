<?php
/**
 * Role Custom Eğitmen Bakiye Yöneticisi
 * 
 * @package Role_Custom
 * @subpackage Withdraw
 * @since 1.4.0
 */

// <PERSON>rudan erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Egitmen bakiyelerini yoneten sinif
 */
class Role_Custom_Balance_Manager {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Islem tipleri
     */
    const TRANSACTION_COURSE_SALE = 'course_sale';
    const TRANSACTION_PRODUCT_SALE = 'product_sale';
    const TRANSACTION_COMMISSION = 'commission';
    const TRANSACTION_WITHDRAW = 'withdraw';
    const TRANSACTION_REFUND = 'refund';
    const TRANSACTION_MANUAL = 'manual';
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Hook'lari baslat
     */
    private function init_hooks() {
        // WooCommerce siparis durum degisiklikleri
        add_action('woocommerce_order_status_completed', [$this, 'handle_order_completed'], 10, 1);
        add_action('woocommerce_order_status_processing', [$this, 'handle_order_processing'], 10, 1);
        add_action('woocommerce_order_status_refunded', [$this, 'handle_order_refunded'], 10, 1);
        
        // Tutor LMS kurs satis hook'lari
        add_action('tutor_order_status_completed', [$this, 'handle_tutor_order_completed'], 10, 1);
        add_action('tutor_course_complete_after', [$this, 'handle_course_completion_bonus'], 10, 2);
        
        // Manuel bakiye islemleri
        add_action('wp_ajax_role_custom_add_manual_balance', [$this, 'ajax_add_manual_balance']);
        add_action('wp_ajax_role_custom_deduct_manual_balance', [$this, 'ajax_deduct_manual_balance']);
    }
    
    /**
     * Bakiye islemini kaydet
     */
    public function add_balance_transaction($user_id, $transaction_data) {
        global $wpdb;
        
        // Egitmen kontrolu
        if (!$this->is_instructor($user_id)) {
            return new WP_Error('invalid_user', 'Kullanici egitmen degil.');
        }
        
        // Mevcut bakiyeyi al
        $current_balance = $this->get_instructor_balance($user_id);
        
        // Yeni bakiyeyi hesapla
        $debit = floatval($transaction_data['debit'] ?? 0);
        $credit = floatval($transaction_data['credit'] ?? 0);
        $new_balance = $current_balance + $debit - $credit;
        
        // Negatif bakiye kontrolu (sadece withdraw islemleri icin)
        if ($new_balance < 0 && $transaction_data['transaction_type'] === self::TRANSACTION_WITHDRAW) {
            return new WP_Error('insufficient_balance', 'Yetersiz bakiye.');
        }
        
        $table_name = $wpdb->prefix . ROLE_CUSTOM_INSTRUCTOR_BALANCE_TABLE;
        
        $result = $wpdb->insert(
            $table_name,
            [
                'user_id' => $user_id,
                'transaction_id' => $transaction_data['transaction_id'] ?? 0,
                'transaction_type' => $transaction_data['transaction_type'],
                'description' => $transaction_data['description'] ?? '',
                'debit' => $debit,
                'credit' => $credit,
                'balance' => $new_balance,
                'reference_id' => $transaction_data['reference_id'] ?? null,
                'reference_type' => $transaction_data['reference_type'] ?? null,
                'status' => $transaction_data['status'] ?? 'completed',
                'created_at' => current_time('mysql')
            ],
            [
                '%d', '%d', '%s', '%s', '%f', '%f', '%f', '%d', '%s', '%s', '%s'
            ]
        );
        
        if ($result === false) {
            return new WP_Error('db_error', 'Veritabani hatasi olustu.');
        }
        
        // Hook tetikle
        do_action('role_custom_balance_updated', $user_id, $new_balance, $transaction_data);
        
        return $wpdb->insert_id;
    }
    
    /**
     * Egitmenin mevcut bakiyesini al
     */
    public function get_instructor_balance($user_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . ROLE_CUSTOM_INSTRUCTOR_BALANCE_TABLE;
        
        $balance = $wpdb->get_var($wpdb->prepare(
            "SELECT balance FROM {$table_name} 
             WHERE user_id = %d 
             ORDER BY id DESC 
             LIMIT 1",
            $user_id
        ));
        
        return $balance ? floatval($balance) : 0.0;
    }
    
    /**
     * WooCommerce siparis tamamlandigi zaman
     */
    public function handle_order_completed($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }
        
        // Siparis zaten islenmis mi kontrol et
        if ($order->get_meta('_role_custom_balance_processed')) {
            return;
        }
        
        // Siparis ogelerini isle
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            if (!$product) {
                continue;
            }
            
            // Urun sahibini al (egitmen)
            $instructor_id = $this->get_product_instructor($product->get_id());
            if (!$instructor_id) {
                continue;
            }
            
            // Komisyon hesapla
            $commission = $this->calculate_instructor_commission($item, $order);
            
            if ($commission > 0) {
                // Bakiyeye ekle
                $this->add_balance_transaction($instructor_id, [
                    'transaction_id' => $order_id,
                    'transaction_type' => self::TRANSACTION_PRODUCT_SALE,
                    'description' => sprintf('Urun satisi: %s', $product->get_name()),
                    'debit' => $commission,
                    'credit' => 0,
                    'reference_id' => $product->get_id(),
                    'reference_type' => 'product',
                    'status' => 'completed'
                ]);
            }
        }
        
        // Islendi olarak isaretle
        $order->update_meta_data('_role_custom_balance_processed', true);
        $order->save();
    }
    
    /**
     * Urunun egitmenini al
     */
    private function get_product_instructor($product_id) {
        // Urun yazarini al
        $product_author = get_post_field('post_author', $product_id);
        
        // Egitmen mi kontrol et
        if ($this->is_instructor($product_author)) {
            return $product_author;
        }
        
        return null;
    }
    
    /**
     * Egitmen komisyonunu hesapla
     */
    private function calculate_instructor_commission($item, $order) {
        // Varsayilan komisyon orani %70 (egitmene)
        $commission_rate = apply_filters('role_custom_instructor_commission_rate', 0.70);
        
        // Urun toplam fiyati
        $item_total = $item->get_total();
        
        // Komisyon hesapla
        $commission = $item_total * $commission_rate;
        
        return apply_filters('role_custom_calculated_instructor_commission', $commission, $item, $order);
    }
    
    /**
     * Tutor LMS siparis tamamlandigi zaman
     */
    public function handle_tutor_order_completed($order_id) {
        // Tutor LMS siparisleri icin ozel islem
        if (!function_exists('tutor')) {
            return;
        }
        
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }
        
        // Tutor kurs siparisleri icin komisyon hesapla
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            if (!$product) {
                continue;
            }
            
            // Kurs mu kontrol et
            $course_id = tutor_utils()->get_course_id_by_product($product->get_id());
            if (!$course_id) {
                continue;
            }
            
            // Kurs egitmenini al
            $instructor_id = get_post_field('post_author', $course_id);
            if (!$this->is_instructor($instructor_id)) {
                continue;
            }
            
            // Kurs komisyonu hesapla
            $commission = $this->calculate_course_commission($item, $order, $course_id);
            
            if ($commission > 0) {
                // Bakiyeye ekle
                $this->add_balance_transaction($instructor_id, [
                    'transaction_id' => $order_id,
                    'transaction_type' => self::TRANSACTION_COURSE_SALE,
                    'description' => sprintf('Kurs satisi: %s', get_the_title($course_id)),
                    'debit' => $commission,
                    'credit' => 0,
                    'reference_id' => $course_id,
                    'reference_type' => 'course',
                    'status' => 'completed'
                ]);
            }
        }
    }
    
    /**
     * Kurs komisyonunu hesapla
     */
    private function calculate_course_commission($item, $order, $course_id) {
        // Kurs icin ozel komisyon orani
        $commission_rate = apply_filters('role_custom_course_commission_rate', 0.75, $course_id);
        
        // Urun toplam fiyati
        $item_total = $item->get_total();
        
        // Komisyon hesapla
        $commission = $item_total * $commission_rate;
        
        return apply_filters('role_custom_calculated_course_commission', $commission, $item, $order, $course_id);
    }
    
    /**
     * Siparis iade edildiginde
     */
    public function handle_order_refunded($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }
        
        // Iade tutarini hesapla ve egitmen bakiyesinden dus
        $refund_amount = $order->get_total_refunded();
        
        if ($refund_amount > 0) {
            // Siparis ogelerini isle
            foreach ($order->get_items() as $item_id => $item) {
                $product = $item->get_product();
                if (!$product) {
                    continue;
                }
                
                $instructor_id = $this->get_product_instructor($product->get_id());
                if (!$instructor_id) {
                    continue;
                }
                
                // Iade komisyonu hesapla
                $refund_commission = $this->calculate_refund_commission($item, $order, $refund_amount);
                
                if ($refund_commission > 0) {
                    // Bakiyeden dus
                    $this->add_balance_transaction($instructor_id, [
                        'transaction_id' => $order_id,
                        'transaction_type' => self::TRANSACTION_REFUND,
                        'description' => sprintf('Urun iadesi: %s', $product->get_name()),
                        'debit' => 0,
                        'credit' => $refund_commission,
                        'reference_id' => $product->get_id(),
                        'reference_type' => 'refund',
                        'status' => 'completed'
                    ]);
                }
            }
        }
    }
    
    /**
     * Iade komisyonunu hesapla
     */
    private function calculate_refund_commission($item, $order, $refund_amount) {
        // Orantili iade hesapla
        $item_total = $item->get_total();
        $order_total = $order->get_total();
        
        if ($order_total > 0) {
            $item_refund_ratio = $item_total / $order_total;
            $item_refund_amount = $refund_amount * $item_refund_ratio;
            
            // Komisyon oranini uygula
            $commission_rate = apply_filters('role_custom_instructor_commission_rate', 0.70);
            return $item_refund_amount * $commission_rate;
        }
        
        return 0;
    }
    
    /**
     * Kullanici egitmen mi kontrol et
     */
    private function is_instructor($user_id) {
        $user = get_userdata($user_id);
        return $user && in_array('tutor_instructor', $user->roles);
    }

    /**
     * Manuel bakiye ekleme (AJAX)
     */
    public function ajax_add_manual_balance() {
        // Yetki kontrolu
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Yetkisiz erisim.');
        }

        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_admin_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $user_id = intval($_POST['user_id']);
        $amount = floatval($_POST['amount']);
        $description = sanitize_textarea_field($_POST['description']);

        if ($amount <= 0) {
            wp_send_json_error('Gecersiz tutar.');
        }

        if (!$this->is_instructor($user_id)) {
            wp_send_json_error('Kullanici egitmen degil.');
        }

        $result = $this->add_balance_transaction($user_id, [
            'transaction_id' => 0,
            'transaction_type' => self::TRANSACTION_MANUAL,
            'description' => $description ?: 'Manuel bakiye ekleme',
            'debit' => $amount,
            'credit' => 0,
            'reference_id' => get_current_user_id(),
            'reference_type' => 'manual_add',
            'status' => 'completed'
        ]);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success('Bakiye basariyla eklendi.');
    }

    /**
     * Manuel bakiye dusme (AJAX)
     */
    public function ajax_deduct_manual_balance() {
        // Yetki kontrolu
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Yetkisiz erisim.');
        }

        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_admin_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $user_id = intval($_POST['user_id']);
        $amount = floatval($_POST['amount']);
        $description = sanitize_textarea_field($_POST['description']);

        if ($amount <= 0) {
            wp_send_json_error('Gecersiz tutar.');
        }

        if (!$this->is_instructor($user_id)) {
            wp_send_json_error('Kullanici egitmen degil.');
        }

        // Mevcut bakiyeyi kontrol et
        $current_balance = $this->get_instructor_balance($user_id);
        if ($current_balance < $amount) {
            wp_send_json_error('Yetersiz bakiye.');
        }

        $result = $this->add_balance_transaction($user_id, [
            'transaction_id' => 0,
            'transaction_type' => self::TRANSACTION_MANUAL,
            'description' => $description ?: 'Manuel bakiye dusme',
            'debit' => 0,
            'credit' => $amount,
            'reference_id' => get_current_user_id(),
            'reference_type' => 'manual_deduct',
            'status' => 'completed'
        ]);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success('Bakiye basariyla dusuldu.');
    }

    /**
     * Egitmenin bakiye gecmisini al
     */
    public function get_balance_history($user_id, $limit = 20, $offset = 0) {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_INSTRUCTOR_BALANCE_TABLE;

        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$table_name}
             WHERE user_id = %d
             ORDER BY created_at DESC
             LIMIT %d OFFSET %d",
            $user_id,
            $limit,
            $offset
        ));
    }

    /**
     * Tum egitmenlerin bakiye ozeti
     */
    public function get_all_instructors_balance_summary() {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_INSTRUCTOR_BALANCE_TABLE;
        $users_table = $wpdb->users;
        $usermeta_table = $wpdb->usermeta;

        return $wpdb->get_results("
            SELECT
                u.ID as user_id,
                u.display_name,
                u.user_email,
                COALESCE(b.balance, 0) as current_balance,
                COALESCE(b.last_transaction_date, '') as last_transaction_date
            FROM {$users_table} u
            INNER JOIN {$usermeta_table} um ON u.ID = um.user_id
            LEFT JOIN (
                SELECT
                    user_id,
                    balance,
                    created_at as last_transaction_date,
                    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY id DESC) as rn
                FROM {$table_name}
            ) b ON u.ID = b.user_id AND b.rn = 1
            WHERE um.meta_key = '{$wpdb->prefix}capabilities'
            AND um.meta_value LIKE '%tutor_instructor%'
            ORDER BY u.display_name ASC
        ");
    }

    /**
     * Bakiye istatistiklerini al
     */
    public function get_balance_statistics() {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_INSTRUCTOR_BALANCE_TABLE;

        $stats = $wpdb->get_row("
            SELECT
                COUNT(DISTINCT user_id) as total_instructors,
                SUM(CASE WHEN debit > 0 THEN debit ELSE 0 END) as total_earnings,
                SUM(CASE WHEN credit > 0 THEN credit ELSE 0 END) as total_withdrawals,
                COUNT(CASE WHEN transaction_type = 'course_sale' THEN 1 END) as course_sales_count,
                COUNT(CASE WHEN transaction_type = 'product_sale' THEN 1 END) as product_sales_count,
                COUNT(CASE WHEN transaction_type = 'withdraw' THEN 1 END) as withdrawals_count
            FROM {$table_name}
            WHERE status = 'completed'
        ", ARRAY_A);

        // Toplam mevcut bakiye
        $total_current_balance = $wpdb->get_var("
            SELECT SUM(balance)
            FROM (
                SELECT
                    user_id,
                    balance,
                    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY id DESC) as rn
                FROM {$table_name}
            ) latest_balances
            WHERE rn = 1
        ");

        $stats['total_current_balance'] = floatval($total_current_balance);

        return $stats;
    }

    /**
     * Belirli tarih araligindaki islemleri al
     */
    public function get_transactions_by_date_range($start_date, $end_date, $user_id = null) {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_INSTRUCTOR_BALANCE_TABLE;

        $where_clause = "WHERE created_at BETWEEN %s AND %s";
        $params = [$start_date, $end_date];

        if ($user_id) {
            $where_clause .= " AND user_id = %d";
            $params[] = $user_id;
        }

        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$table_name}
             {$where_clause}
             ORDER BY created_at DESC",
            $params
        ));
    }
}
