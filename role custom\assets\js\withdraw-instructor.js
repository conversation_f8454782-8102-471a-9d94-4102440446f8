/**
 * Role Custom Eğitmen Para Çekme JavaScript
 * 
 * @package Role_Custom
 * @subpackage Assets
 * @since 1.4.0
 */

(function($) {
    'use strict';

    // <PERSON><PERSON> yüklendiğinde çalışacak fonksiyonlar
    $(document).ready(function() {
        initInstructorWithdraw();
    });

    /**
     * Eğitmen para çekme sistemini başlat
     */
    function initInstructorWithdraw() {
        // Event'leri bağla
        bindEvents();
        
        // Form validasyonunu başlat
        initFormValidation();
    }

    /**
     * Event'leri bağla
     */
    function bindEvents() {
        // Yeni para çekme formu göster/gizle
        $(document).on('click', '#new-withdraw-btn', showWithdrawForm);
        $(document).on('click', '#cancel-withdraw', hideWithdrawForm);

        // Ödeme yöntemi değişikliği
        $(document).on('change', '#withdraw-method', handleMethodChange);

        // <PERSON><PERSON> değişikliği
        $(document).on('input', '#withdraw-amount', calculateCharges);

        // Form gönderimi
        $(document).on('submit', '#withdraw-form', handleFormSubmit);

        // Detay görüntüleme
        $(document).on('click', '.view-details', handleViewDetails);

        // Talep iptal etme
        $(document).on('click', '.cancel-request', handleCancelRequest);
    }

    /**
     * Para çekme formunu göster
     */
    function showWithdrawForm() {
        $('#withdraw-form-container').slideDown(300);
        $('#new-withdraw-btn').prop('disabled', true).text('Form Açık');
        
        // Formu sıfırla
        $('#withdraw-form')[0].reset();
        $('.payment-details').hide();
        $('#charge-info').hide();
    }

    /**
     * Para çekme formunu gizle
     */
    function hideWithdrawForm() {
        $('#withdraw-form-container').slideUp(300);
        $('#new-withdraw-btn').prop('disabled', false).text('Para Çek');
    }

    /**
     * Ödeme yöntemi değişikliğini işle
     */
    function handleMethodChange() {
        var method = $(this).val();
        
        // Tüm ödeme detaylarını gizle
        $('.payment-details').hide();
        
        // Seçilen yönteme göre ilgili alanı göster
        if (method === 'bank') {
            $('#bank-details').show();
            makeFieldsRequired('#bank-details input');
        } else if (method === 'paypal') {
            $('#paypal-details').show();
            makeFieldsRequired('#paypal-details input');
        }
        
        // Ücret hesapla
        calculateCharges();
    }

    /**
     * Alanları zorunlu yap
     */
    function makeFieldsRequired(selector) {
        // Önce tüm ödeme alanlarının zorunluluğunu kaldır
        $('.payment-details input').prop('required', false);
        
        // Seçilen alanları zorunlu yap
        $(selector).prop('required', true);
    }

    /**
     * Ücretleri hesapla ve göster
     */
    function calculateCharges() {
        var amount = parseFloat($('#withdraw-amount').val()) || 0;
        var method = $('#withdraw-method').val();
        
        if (amount > 0 && method) {
            // AJAX ile ücret hesaplama
            $.ajax({
                url: roleCustomWithdraw.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'role_custom_calculate_withdraw_charges',
                    amount: amount,
                    method: method,
                    nonce: roleCustomWithdraw.nonce
                },
                success: function(response) {
                    if (response.success) {
                        displayChargeInfo(response.data);
                    }
                }
            });
        } else {
            $('#charge-info').hide();
        }
    }

    /**
     * Ücret bilgisini göster
     */
    function displayChargeInfo(data) {
        $('#requested-amount').text(data.requested_amount_formatted);
        $('#charge-amount').text(data.charge_amount_formatted);
        $('#receivable-amount').text(data.receivable_amount_formatted);
        $('#charge-info').show();
    }

    /**
     * Form validasyonunu başlat
     */
    function initFormValidation() {
        // IBAN formatı kontrolü
        $(document).on('input', '#iban', function() {
            var iban = $(this).val().replace(/\s/g, '').toUpperCase();
            var formatted = iban.replace(/(.{4})/g, '$1 ').trim();
            $(this).val(formatted);
        });

        // Tutor kontrolü
        $(document).on('input', '#withdraw-amount', function() {
            var amount = parseFloat($(this).val()) || 0;
            var maxAmount = parseFloat($(this).attr('max')) || 0;
            var minAmount = parseFloat($(this).attr('min')) || 0;
            
            if (amount > maxAmount) {
                $(this).val(maxAmount);
                showNotice('Maksimum çekebileceğiniz tutar: ' + formatCurrency(maxAmount), 'warning');
            } else if (amount < minAmount && amount > 0) {
                showNotice('Minimum çekme tutarı: ' + formatCurrency(minAmount), 'warning');
            }
        });
    }

    /**
     * Form gönderimini işle
     */
    function handleFormSubmit(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $('#submit-withdraw');
        
        // Validasyon
        if (!validateForm($form)) {
            return;
        }
        
        // Onay al
        if (!confirm('Para çekme talebinizi göndermek istediğinizden emin misiniz?')) {
            return;
        }
        
        // Loading durumu
        setButtonLoading($submitBtn, true);
        
        // AJAX gönderimi
        $.ajax({
            url: roleCustomWithdraw.ajaxUrl,
            type: 'POST',
            data: $form.serialize() + '&action=role_custom_submit_withdraw_request',
            success: function(response) {
                setButtonLoading($submitBtn, false);
                
                if (response.success) {
                    showNotice('Para çekme talebiniz başarıyla gönderildi.', 'success');
                    hideWithdrawForm();
                    
                    // Sayfayı yenile
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showNotice(response.data.message || 'Bir hata oluştu.', 'error');
                }
            },
            error: function() {
                setButtonLoading($submitBtn, false);
                showNotice('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
            }
        });
    }

    /**
     * Form validasyonu
     */
    function validateForm($form) {
        var isValid = true;
        var amount = parseFloat($('#withdraw-amount').val()) || 0;
        var method = $('#withdraw-method').val();
        
        // Tutar kontrolü
        if (amount <= 0) {
            showNotice('Geçerli bir tutar girin.', 'error');
            isValid = false;
        }
        
        // Ödeme yöntemi kontrolü
        if (!method) {
            showNotice('Ödeme yöntemi seçin.', 'error');
            isValid = false;
        }
        
        // Ödeme detayları kontrolü
        if (method === 'bank') {
            var requiredFields = ['#bank-name', '#account-holder', '#account-number', '#iban'];
            requiredFields.forEach(function(field) {
                if (!$(field).val().trim()) {
                    showNotice('Tüm banka bilgilerini doldurun.', 'error');
                    isValid = false;
                    return false;
                }
            });
        } else if (method === 'paypal') {
            if (!$('#paypal-email').val().trim()) {
                showNotice('PayPal e-posta adresini girin.', 'error');
                isValid = false;
            }
        }
        
        return isValid;
    }

    /**
     * Detay görüntüleme
     */
    function handleViewDetails() {
        var withdrawId = $(this).data('id');
        
        // Modal veya detay sayfası açılabilir
        // Şimdilik basit alert
        alert('Detay görüntüleme özelliği yakında eklenecek. ID: ' + withdrawId);
    }

    /**
     * Talep iptal etme
     */
    function handleCancelRequest() {
        var withdrawId = $(this).data('id');
        
        if (!confirm('Para çekme talebinizi iptal etmek istediğinizden emin misiniz?')) {
            return;
        }
        
        var $btn = $(this);
        $btn.prop('disabled', true).text('İptal Ediliyor...');
        
        $.ajax({
            url: roleCustomWithdraw.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_cancel_withdraw_request',
                withdraw_id: withdrawId,
                nonce: roleCustomWithdraw.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice('Para çekme talebiniz iptal edildi.', 'success');
                    
                    // Sayfayı yenile
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showNotice(response.data.message || 'Bir hata oluştu.', 'error');
                    $btn.prop('disabled', false).text('İptal Et');
                }
            },
            error: function() {
                showNotice('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
                $btn.prop('disabled', false).text('İptal Et');
            }
        });
    }

    /**
     * Bildirim göster
     */
    function showNotice(message, type) {
        var noticeClass = 'notice-' + type;
        var notice = '<div class="role-custom-notice ' + noticeClass + '" style="margin: 15px 0; padding: 12px; border-left: 4px solid; border-radius: 4px;"><p style="margin: 0;">' + message + '</p></div>';
        
        // Renk ayarları
        var colors = {
            'success': { bg: '#f0f8ff', border: '#00a32a', color: '#00a32a' },
            'error': { bg: '#fef7f7', border: '#d63638', color: '#d63638' },
            'warning': { bg: '#fcf9e8', border: '#dba617', color: '#dba617' }
        };
        
        var $notice = $(notice);
        if (colors[type]) {
            $notice.css({
                'background-color': colors[type].bg,
                'border-left-color': colors[type].border,
                'color': colors[type].color
            });
        }
        
        // Mevcut bildirimleri kaldır
        $('.role-custom-notice').remove();
        
        // Yeni bildirimi ekle
        $('.role-custom-instructor-withdraw').prepend($notice);
        
        // Otomatik kaldırma
        setTimeout(function() {
            $notice.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    /**
     * Buton loading durumu
     */
    function setButtonLoading($button, loading) {
        if (loading) {
            $button.prop('disabled', true);
            $button.data('original-text', $button.text());
            $button.text('İşleniyor...');
        } else {
            $button.prop('disabled', false);
            $button.text($button.data('original-text') || $button.text());
        }
    }

    /**
     * Para formatı
     */
    function formatCurrency(amount) {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(amount);
    }

})(jQuery);
