<?php
/**
 * Role Custom Para Çekme Sistemi Test Sınıfı
 * 
 * @package Role_Custom
 * @subpackage Withdraw
 * @since 1.4.0
 */

// Dogrudan erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Para çekme sistemi test ve dogrulama sinifi
 */
class Role_Custom_Withdraw_Tests {
    
    /**
     * Test sonuclari
     */
    private $test_results = [];
    
    /**
     * Test verilerini olustur
     */
    public function create_test_data() {
        // Test egitmeni olustur
        $instructor_id = $this->create_test_instructor();
        
        // Test bakiyesi ekle
        $this->add_test_balance($instructor_id, 1000);
        
        // Test para çekme talebi olustur
        $withdraw_id = $this->create_test_withdraw_request($instructor_id, 500);
        
        return [
            'instructor_id' => $instructor_id,
            'withdraw_id' => $withdraw_id
        ];
    }
    
    /**
     * Test egitmeni olustur
     */
    private function create_test_instructor() {
        $username = 'test_instructor_' . time();
        $email = $username . '@test.com';
        
        $user_id = wp_create_user($username, 'test123', $email);
        
        if (is_wp_error($user_id)) {
            return false;
        }
        
        // Egitmen rolunu ekle
        $user = new WP_User($user_id);
        $user->add_role('tutor_instructor');
        
        return $user_id;
    }
    
    /**
     * Test bakiyesi ekle
     */
    private function add_test_balance($user_id, $amount) {
        $balance_manager = Role_Custom_Balance_Manager::get_instance();
        
        return $balance_manager->add_balance_transaction($user_id, [
            'transaction_id' => 0,
            'transaction_type' => Role_Custom_Balance_Manager::TRANSACTION_MANUAL,
            'description' => 'Test bakiyesi',
            'debit' => $amount,
            'credit' => 0,
            'reference_id' => 0,
            'reference_type' => 'test',
            'status' => 'completed'
        ]);
    }
    
    /**
     * Test para çekme talebi olustur
     */
    private function create_test_withdraw_request($user_id, $amount) {
        $withdraw_manager = Role_Custom_Withdraw_Manager::get_instance();
        
        return $withdraw_manager->create_withdraw_request(
            $user_id,
            $amount,
            'bank',
            'Test para çekme talebi',
            [
                'bank_name' => 'Test Bankasi',
                'account_holder' => 'Test Kullanici',
                'account_number' => '*********',
                'iban' => 'TR00 0000 0000 0000 0000 0000 00'
            ]
        );
    }
    
    /**
     * Tum testleri calistir
     */
    public function run_all_tests() {
        $this->test_results = [];
        
        // Veritabani testleri
        $this->test_database_tables();
        
        // Sinif testleri
        $this->test_class_instances();
        
        // Fonksiyonel testleri
        $this->test_withdraw_functionality();
        
        // Guvenlik testleri
        $this->test_security_features();
        
        // Bakiye testleri
        $this->test_balance_management();
        
        return $this->test_results;
    }
    
    /**
     * Veritabani tablolarini test et
     */
    private function test_database_tables() {
        global $wpdb;
        
        $tables = [
            $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE,
            $wpdb->prefix . ROLE_CUSTOM_INSTRUCTOR_BALANCE_TABLE,
            $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TRANSACTIONS_TABLE,
            $wpdb->prefix . 'role_custom_security_logs'
        ];
        
        foreach ($tables as $table) {
            $exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'") === $table;
            $this->add_test_result('Database', "Tablo {$table}", $exists, $exists ? 'Tablo mevcut' : 'Tablo bulunamadi');
        }
    }
    
    /**
     * Sinif orneklerini test et
     */
    private function test_class_instances() {
        $classes = [
            'Role_Custom_Withdraw_Manager' => Role_Custom_Withdraw_Manager::get_instance(),
            'Role_Custom_Balance_Manager' => Role_Custom_Balance_Manager::get_instance(),
            'Role_Custom_Withdraw_Security' => Role_Custom_Withdraw_Security::get_instance()
        ];
        
        foreach ($classes as $class_name => $instance) {
            $exists = is_object($instance) && get_class($instance) === $class_name;
            $this->add_test_result('Classes', $class_name, $exists, $exists ? 'Sinif yuklendi' : 'Sinif yuklenemedi');
        }
    }
    
    /**
     * Para çekme fonksiyonalitesini test et
     */
    private function test_withdraw_functionality() {
        // Test verilerini olustur
        $test_data = $this->create_test_data();
        
        if (!$test_data) {
            $this->add_test_result('Withdraw', 'Test verisi olusturma', false, 'Test verisi olusturulamadi');
            return;
        }
        
        $instructor_id = $test_data['instructor_id'];
        $withdraw_id = $test_data['withdraw_id'];
        
        // Para çekme talebi olusturma testi
        $success = !is_wp_error($withdraw_id) && $withdraw_id > 0;
        $this->add_test_result('Withdraw', 'Para çekme talebi olusturma', $success, $success ? 'Talep olusturuldu' : 'Talep olusturulamadi');
        
        if ($success) {
            // Talep onaylama testi
            $withdraw_manager = Role_Custom_Withdraw_Manager::get_instance();
            $approval_result = $withdraw_manager->approve_withdraw($withdraw_id, 'Test onay');
            
            $approval_success = !is_wp_error($approval_result);
            $this->add_test_result('Withdraw', 'Para çekme talebi onaylama', $approval_success, $approval_success ? 'Talep onaylandi' : 'Talep onaylanamadi');
        }
        
        // Test verilerini temizle
        $this->cleanup_test_data($instructor_id);
    }
    
    /**
     * Guvenlik ozelliklerini test et
     */
    private function test_security_features() {
        $security = Role_Custom_Withdraw_Security::get_instance();
        
        // Guvenlik ayarlarini test et
        $settings = $security->get_security_settings();
        $settings_exist = is_array($settings) && !empty($settings);
        $this->add_test_result('Security', 'Guvenlik ayarlari', $settings_exist, $settings_exist ? 'Ayarlar yuklendi' : 'Ayarlar yuklenemedi');
        
        // IP dogrulama testi
        $ip_validation = $security->validate_user_ip(true, 1);
        $this->add_test_result('Security', 'IP dogrulama', is_bool($ip_validation), is_bool($ip_validation) ? 'IP dogrulama calisiyor' : 'IP dogrulama hatasi');
        
        // Rate limiting testi
        $rate_limit = $security->check_rate_limit(true, 1);
        $this->add_test_result('Security', 'Rate limiting', is_bool($rate_limit), is_bool($rate_limit) ? 'Rate limiting calisiyor' : 'Rate limiting hatasi');
    }
    
    /**
     * Bakiye yonetimini test et
     */
    private function test_balance_management() {
        $balance_manager = Role_Custom_Balance_Manager::get_instance();
        
        // Test egitmeni olustur
        $instructor_id = $this->create_test_instructor();
        
        if (!$instructor_id) {
            $this->add_test_result('Balance', 'Test egitmeni olusturma', false, 'Egitmen olusturulamadi');
            return;
        }
        
        // Bakiye ekleme testi
        $add_result = $this->add_test_balance($instructor_id, 100);
        $add_success = !is_wp_error($add_result) && $add_result > 0;
        $this->add_test_result('Balance', 'Bakiye ekleme', $add_success, $add_success ? 'Bakiye eklendi' : 'Bakiye eklenemedi');
        
        // Bakiye sorgulama testi
        $balance = $balance_manager->get_instructor_balance($instructor_id);
        $balance_correct = $balance == 100;
        $this->add_test_result('Balance', 'Bakiye sorgulama', $balance_correct, $balance_correct ? "Bakiye dogru: {$balance}" : "Bakiye yanlis: {$balance}");
        
        // Test verilerini temizle
        $this->cleanup_test_data($instructor_id);
    }
    
    /**
     * Test sonucu ekle
     */
    private function add_test_result($category, $test_name, $success, $message) {
        $this->test_results[] = [
            'category' => $category,
            'test' => $test_name,
            'success' => $success,
            'message' => $message,
            'timestamp' => current_time('mysql')
        ];
    }
    
    /**
     * Test verilerini temizle
     */
    private function cleanup_test_data($user_id) {
        if (!$user_id) {
            return;
        }
        
        global $wpdb;
        
        // Kullaniciyi sil
        wp_delete_user($user_id);
        
        // Bakiye kayitlarini sil
        $balance_table = $wpdb->prefix . ROLE_CUSTOM_INSTRUCTOR_BALANCE_TABLE;
        $wpdb->delete($balance_table, ['user_id' => $user_id], ['%d']);
        
        // Para çekme kayitlarini sil
        $withdraw_table = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;
        $wpdb->delete($withdraw_table, ['user_id' => $user_id], ['%d']);
        
        // Guvenlik loglarini sil
        $security_table = $wpdb->prefix . 'role_custom_security_logs';
        $wpdb->delete($security_table, ['user_id' => $user_id], ['%d']);
    }
    
    /**
     * Test raporunu olustur
     */
    public function generate_test_report() {
        $results = $this->run_all_tests();
        
        $total_tests = count($results);
        $passed_tests = count(array_filter($results, function($result) {
            return $result['success'];
        }));
        $failed_tests = $total_tests - $passed_tests;
        
        $report = [
            'summary' => [
                'total' => $total_tests,
                'passed' => $passed_tests,
                'failed' => $failed_tests,
                'success_rate' => $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0
            ],
            'results' => $results,
            'generated_at' => current_time('mysql')
        ];
        
        return $report;
    }
    
    /**
     * Performans testini calistir
     */
    public function run_performance_test() {
        $start_time = microtime(true);
        $start_memory = memory_get_usage();
        
        // Test islemlerini calistir
        $test_data = $this->create_test_data();
        
        if ($test_data) {
            $withdraw_manager = Role_Custom_Withdraw_Manager::get_instance();
            $balance_manager = Role_Custom_Balance_Manager::get_instance();
            
            // Bakiye sorgulama performansi
            for ($i = 0; $i < 100; $i++) {
                $balance_manager->get_instructor_balance($test_data['instructor_id']);
            }
            
            // Para çekme listesi performansi
            for ($i = 0; $i < 50; $i++) {
                $withdraw_manager->get_user_withdraw_history($test_data['instructor_id'], 10);
            }
            
            $this->cleanup_test_data($test_data['instructor_id']);
        }
        
        $end_time = microtime(true);
        $end_memory = memory_get_usage();
        
        return [
            'execution_time' => round(($end_time - $start_time) * 1000, 2), // milisaniye
            'memory_usage' => round(($end_memory - $start_memory) / 1024, 2), // KB
            'peak_memory' => round(memory_get_peak_usage() / 1024 / 1024, 2) // MB
        ];
    }
}
