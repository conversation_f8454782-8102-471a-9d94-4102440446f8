<?php
/**
 * Admin Eğitmen Bakiyeleri Sayfası
 * 
 * @package Role_Custom
 * @subpackage Templates
 * @since 1.4.0
 */

// Dogrudan erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Bakiye yoneticisini al
$balance_manager = Role_Custom_Balance_Manager::get_instance();

// Sayfa parametrelerini al
$current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'overview';

// Egitmen bakiye ozetini al
$instructors_balance = $balance_manager->get_all_instructors_balance_summary();

// Istatistikleri al
$stats = $balance_manager->get_balance_statistics();

?>

<div class="wrap role-custom-instructor-balances">
    <h1 class="wp-heading-inline">
        <?php _e('Eğitmen Bakiyeleri', 'role-custom'); ?>
    </h1>
    
    <!-- Istatistik Kartlari -->
    <div class="role-custom-stats-grid">
        <div class="role-custom-stat-card">
            <div class="stat-number"><?php echo number_format($stats['total_instructors']); ?></div>
            <div class="stat-label"><?php _e('Toplam Eğitmen', 'role-custom'); ?></div>
        </div>
        
        <div class="role-custom-stat-card">
            <div class="stat-number"><?php echo wc_price($stats['total_current_balance']); ?></div>
            <div class="stat-label"><?php _e('Toplam Mevcut Bakiye', 'role-custom'); ?></div>
        </div>
        
        <div class="role-custom-stat-card">
            <div class="stat-number"><?php echo wc_price($stats['total_earnings']); ?></div>
            <div class="stat-label"><?php _e('Toplam Kazanç', 'role-custom'); ?></div>
        </div>
        
        <div class="role-custom-stat-card">
            <div class="stat-number"><?php echo wc_price($stats['total_withdrawals']); ?></div>
            <div class="stat-label"><?php _e('Toplam Çekilen', 'role-custom'); ?></div>
        </div>
    </div>
    
    <!-- Tab Navigasyonu -->
    <nav class="nav-tab-wrapper wp-clearfix">
        <a href="<?php echo admin_url('admin.php?page=role-custom-instructor-balances&tab=overview'); ?>" 
           class="nav-tab <?php echo $current_tab === 'overview' ? 'nav-tab-active' : ''; ?>">
            <?php _e('Genel Bakış', 'role-custom'); ?>
        </a>
        
        <a href="<?php echo admin_url('admin.php?page=role-custom-instructor-balances&tab=transactions'); ?>" 
           class="nav-tab <?php echo $current_tab === 'transactions' ? 'nav-tab-active' : ''; ?>">
            <?php _e('İşlem Geçmişi', 'role-custom'); ?>
        </a>
        
        <a href="<?php echo admin_url('admin.php?page=role-custom-instructor-balances&tab=manual'); ?>" 
           class="nav-tab <?php echo $current_tab === 'manual' ? 'nav-tab-active' : ''; ?>">
            <?php _e('Manuel İşlemler', 'role-custom'); ?>
        </a>
    </nav>
    
    <?php if ($current_tab === 'overview'): ?>
        <!-- Egitmen Bakiye Tablosu -->
        <div class="role-custom-balances-table">
            <?php if (empty($instructors_balance)): ?>
                <div class="role-custom-empty-state">
                    <p><?php _e('Henüz hiç eğitmen bulunamadı.', 'role-custom'); ?></p>
                </div>
            <?php else: ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th scope="col"><?php _e('Eğitmen', 'role-custom'); ?></th>
                            <th scope="col"><?php _e('E-posta', 'role-custom'); ?></th>
                            <th scope="col"><?php _e('Mevcut Bakiye', 'role-custom'); ?></th>
                            <th scope="col"><?php _e('Son İşlem', 'role-custom'); ?></th>
                            <th scope="col"><?php _e('İşlemler', 'role-custom'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($instructors_balance as $instructor): ?>
                            <tr>
                                <td>
                                    <strong><?php echo esc_html($instructor->display_name); ?></strong>
                                </td>
                                <td><?php echo esc_html($instructor->user_email); ?></td>
                                <td>
                                    <strong class="balance-amount <?php echo $instructor->current_balance > 0 ? 'positive' : 'zero'; ?>">
                                        <?php echo wc_price($instructor->current_balance); ?>
                                    </strong>
                                </td>
                                <td>
                                    <?php 
                                    if ($instructor->last_transaction_date) {
                                        echo date_i18n('d.m.Y H:i', strtotime($instructor->last_transaction_date));
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <div class="row-actions">
                                        <span class="view">
                                            <a href="#" class="view-balance-history" data-user-id="<?php echo $instructor->user_id; ?>">
                                                <?php _e('Geçmiş', 'role-custom'); ?>
                                            </a>
                                        </span>
                                        | <span class="add">
                                            <a href="#" class="add-balance" data-user-id="<?php echo $instructor->user_id; ?>" data-user-name="<?php echo esc_attr($instructor->display_name); ?>">
                                                <?php _e('Bakiye Ekle', 'role-custom'); ?>
                                            </a>
                                        </span>
                                        <?php if ($instructor->current_balance > 0): ?>
                                        | <span class="deduct">
                                            <a href="#" class="deduct-balance" data-user-id="<?php echo $instructor->user_id; ?>" data-user-name="<?php echo esc_attr($instructor->display_name); ?>">
                                                <?php _e('Bakiye Düş', 'role-custom'); ?>
                                            </a>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
        
    <?php elseif ($current_tab === 'transactions'): ?>
        <!-- Islem Gecmisi -->
        <div class="role-custom-transactions-section">
            <div class="transaction-filters">
                <form method="get" action="">
                    <input type="hidden" name="page" value="role-custom-instructor-balances">
                    <input type="hidden" name="tab" value="transactions">
                    
                    <label for="filter-instructor"><?php _e('Eğitmen:', 'role-custom'); ?></label>
                    <select name="instructor_id" id="filter-instructor">
                        <option value=""><?php _e('Tüm Eğitmenler', 'role-custom'); ?></option>
                        <?php foreach ($instructors_balance as $instructor): ?>
                            <option value="<?php echo $instructor->user_id; ?>" <?php selected(isset($_GET['instructor_id']) ? $_GET['instructor_id'] : '', $instructor->user_id); ?>>
                                <?php echo esc_html($instructor->display_name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    
                    <label for="filter-type"><?php _e('İşlem Tipi:', 'role-custom'); ?></label>
                    <select name="transaction_type" id="filter-type">
                        <option value=""><?php _e('Tüm İşlemler', 'role-custom'); ?></option>
                        <option value="course_sale" <?php selected(isset($_GET['transaction_type']) ? $_GET['transaction_type'] : '', 'course_sale'); ?>><?php _e('Kurs Satışı', 'role-custom'); ?></option>
                        <option value="product_sale" <?php selected(isset($_GET['transaction_type']) ? $_GET['transaction_type'] : '', 'product_sale'); ?>><?php _e('Ürün Satışı', 'role-custom'); ?></option>
                        <option value="withdraw" <?php selected(isset($_GET['transaction_type']) ? $_GET['transaction_type'] : '', 'withdraw'); ?>><?php _e('Para Çekme', 'role-custom'); ?></option>
                        <option value="refund" <?php selected(isset($_GET['transaction_type']) ? $_GET['transaction_type'] : '', 'refund'); ?>><?php _e('İade', 'role-custom'); ?></option>
                        <option value="manual" <?php selected(isset($_GET['transaction_type']) ? $_GET['transaction_type'] : '', 'manual'); ?>><?php _e('Manuel', 'role-custom'); ?></option>
                    </select>
                    
                    <label for="filter-date-from"><?php _e('Başlangıç:', 'role-custom'); ?></label>
                    <input type="date" name="date_from" id="filter-date-from" value="<?php echo isset($_GET['date_from']) ? esc_attr($_GET['date_from']) : ''; ?>">
                    
                    <label for="filter-date-to"><?php _e('Bitiş:', 'role-custom'); ?></label>
                    <input type="date" name="date_to" id="filter-date-to" value="<?php echo isset($_GET['date_to']) ? esc_attr($_GET['date_to']) : ''; ?>">
                    
                    <input type="submit" class="button" value="<?php _e('Filtrele', 'role-custom'); ?>">
                    <a href="<?php echo admin_url('admin.php?page=role-custom-instructor-balances&tab=transactions'); ?>" class="button"><?php _e('Temizle', 'role-custom'); ?></a>
                </form>
            </div>
            
            <div id="transactions-results">
                <!-- AJAX ile yuklenecek -->
                <p><?php _e('Filtreleri kullanarak işlem geçmişini görüntüleyebilirsiniz.', 'role-custom'); ?></p>
            </div>
        </div>
        
    <?php elseif ($current_tab === 'manual'): ?>
        <!-- Manuel Islemler -->
        <div class="role-custom-manual-section">
            <div class="manual-operations">
                <div class="operation-card">
                    <h3><?php _e('Bakiye Ekle', 'role-custom'); ?></h3>
                    <p><?php _e('Eğitmene manuel olarak bakiye ekleyin.', 'role-custom'); ?></p>
                    <button type="button" class="button button-primary" id="show-add-balance-form">
                        <?php _e('Bakiye Ekle', 'role-custom'); ?>
                    </button>
                </div>
                
                <div class="operation-card">
                    <h3><?php _e('Bakiye Düş', 'role-custom'); ?></h3>
                    <p><?php _e('Eğitmenin bakiyesinden manuel olarak düşüm yapın.', 'role-custom'); ?></p>
                    <button type="button" class="button button-secondary" id="show-deduct-balance-form">
                        <?php _e('Bakiye Düş', 'role-custom'); ?>
                    </button>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Bakiye Geçmişi Modal -->
<div id="balance-history-modal" class="role-custom-modal" style="display: none;">
    <div class="role-custom-modal-content">
        <div class="role-custom-modal-header">
            <h3><?php _e('Bakiye Geçmişi', 'role-custom'); ?></h3>
            <span class="role-custom-modal-close">&times;</span>
        </div>
        <div class="role-custom-modal-body">
            <div id="balance-history-content">
                <!-- İçerik AJAX ile yüklenecek -->
            </div>
        </div>
        <div class="role-custom-modal-footer">
            <button type="button" class="button" id="close-history-modal"><?php _e('Kapat', 'role-custom'); ?></button>
        </div>
    </div>
</div>

<!-- Manuel Bakiye İşlemi Modal -->
<div id="manual-balance-modal" class="role-custom-modal" style="display: none;">
    <div class="role-custom-modal-content">
        <div class="role-custom-modal-header">
            <h3 id="manual-modal-title"><?php _e('Manuel Bakiye İşlemi', 'role-custom'); ?></h3>
            <span class="role-custom-modal-close">&times;</span>
        </div>
        <div class="role-custom-modal-body">
            <form id="manual-balance-form">
                <input type="hidden" id="manual-user-id" name="user_id" value="">
                <input type="hidden" id="manual-operation" name="operation" value="">
                
                <div class="form-field">
                    <label for="manual-instructor"><?php _e('Eğitmen:', 'role-custom'); ?></label>
                    <select id="manual-instructor" name="instructor_select" required>
                        <option value=""><?php _e('Eğitmen Seçin...', 'role-custom'); ?></option>
                        <?php foreach ($instructors_balance as $instructor): ?>
                            <option value="<?php echo $instructor->user_id; ?>" data-name="<?php echo esc_attr($instructor->display_name); ?>">
                                <?php echo esc_html($instructor->display_name); ?> (<?php echo wc_price($instructor->current_balance); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-field">
                    <label for="manual-amount"><?php _e('Tutar:', 'role-custom'); ?></label>
                    <input type="number" id="manual-amount" name="amount" min="0.01" step="0.01" required>
                </div>
                
                <div class="form-field">
                    <label for="manual-description"><?php _e('Açıklama:', 'role-custom'); ?></label>
                    <textarea id="manual-description" name="description" rows="3" placeholder="<?php _e('İşlem açıklaması...', 'role-custom'); ?>"></textarea>
                </div>
            </form>
        </div>
        <div class="role-custom-modal-footer">
            <button type="button" class="button" id="cancel-manual"><?php _e('İptal', 'role-custom'); ?></button>
            <button type="button" class="button button-primary" id="confirm-manual"><?php _e('İşlemi Gerçekleştir', 'role-custom'); ?></button>
        </div>
    </div>
</div>
