/**
 * Role Custom Eğitmen Para Çekme CSS
 * 
 * @package Role_Custom
 * @subpackage Assets
 * @since 1.4.0
 */

/* Genel Container */
.role-custom-instructor-withdraw {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.withdraw-header {
    margin-bottom: 30px;
    text-align: center;
}

.withdraw-header h2 {
    color: #1d2327;
    margin-bottom: 10px;
    font-size: 28px;
}

.withdraw-header .description {
    color: #646970;
    font-size: 16px;
    margin: 0;
}

/* <PERSON><PERSON><PERSON> */
.balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.balance-info h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    opacity: 0.9;
}

.balance-amount {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
}

.balance-note {
    margin: 0;
    opacity: 0.8;
    font-size: 14px;
}

.balance-actions .button {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 6px;
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    transition: all 0.3s ease;
}

.balance-actions .button:hover:not(:disabled) {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
}

.balance-actions .button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Para Çekme Formu */
.withdraw-form-container {
    margin-bottom: 30px;
}

.withdraw-form-card {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.withdraw-form-card h3 {
    margin: 0 0 25px 0;
    color: #1d2327;
    font-size: 22px;
    border-bottom: 2px solid #f0f0f1;
    padding-bottom: 15px;
}

/* Form Stilleri */
.form-row {
    margin-bottom: 20px;
}

.form-row label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #1d2327;
    font-size: 14px;
}

.form-row input,
.form-row select,
.form-row textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-row input:focus,
.form-row select:focus,
.form-row textarea:focus {
    outline: none;
    border-color: #2271b1;
    box-shadow: 0 0 0 3px rgba(34, 113, 177, 0.1);
}

.form-help {
    display: block;
    margin-top: 5px;
    color: #646970;
    font-size: 12px;
    font-style: italic;
}

/* Tutar Input Grubu */
.amount-input-group {
    position: relative;
}

.amount-input-group input {
    padding-right: 50px;
}

.currency-symbol {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #646970;
    font-weight: 600;
}

/* Ödeme Detayları */
.payment-details {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 20px;
    margin-top: 15px;
}

.payment-details h4 {
    margin: 0 0 15px 0;
    color: #1d2327;
    font-size: 16px;
}

/* Ücret Bilgisi */
.charge-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.charge-breakdown {
    font-size: 14px;
}

.charge-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
}

.charge-row:not(.total) {
    border-bottom: 1px solid #f0f0f1;
}

.charge-row.total {
    font-weight: bold;
    font-size: 16px;
    color: #1d2327;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Form Aksiyonları */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f1;
}

.form-actions .button {
    padding: 12px 24px;
    font-size: 14px;
    border-radius: 6px;
    min-width: 120px;
}

/* Para Çekme Geçmişi */
.withdraw-history {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.withdraw-history h3 {
    margin: 0 0 25px 0;
    color: #1d2327;
    font-size: 22px;
    border-bottom: 2px solid #f0f0f1;
    padding-bottom: 15px;
}

/* Boş Durum */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #646970;
}

.empty-state p {
    margin: 0;
    font-size: 16px;
}

/* Geçmiş Tablosu */
.history-table {
    overflow-x: auto;
}

.withdraw-history-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.withdraw-history-table th,
.withdraw-history-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f1;
}

.withdraw-history-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #1d2327;
    border-bottom: 2px solid #e1e5e9;
}

.withdraw-history-table tr:hover {
    background: #f8f9fa;
}

/* Durum Etiketleri */
.status-pending {
    color: #d63638;
    font-weight: 600;
    background: #fef7f7;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-approved {
    color: #00a32a;
    font-weight: 600;
    background: #f0f8ff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-cancelled {
    color: #646970;
    font-weight: 600;
    background: #f6f7f7;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-processing {
    color: #dba617;
    font-weight: 600;
    background: #fcf9e8;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

/* Buton Link */
.button-link {
    background: none;
    border: none;
    color: #2271b1;
    cursor: pointer;
    text-decoration: underline;
    font-size: 13px;
    padding: 0;
}

.button-link:hover {
    color: #135e96;
}

/* Responsive */
@media (max-width: 768px) {
    .role-custom-instructor-withdraw {
        padding: 15px;
    }
    
    .balance-card {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .withdraw-form-card,
    .withdraw-history {
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .button {
        width: 100%;
    }
    
    .withdraw-history-table {
        font-size: 12px;
    }
    
    .withdraw-history-table th,
    .withdraw-history-table td {
        padding: 8px 10px;
    }
}

@media (max-width: 480px) {
    .balance-amount {
        font-size: 28px;
    }
    
    .withdraw-header h2 {
        font-size: 24px;
    }
    
    .withdraw-form-card h3,
    .withdraw-history h3 {
        font-size: 18px;
    }
}

/* Loading ve Animasyonlar */
.role-custom-loading {
    text-align: center;
    padding: 20px;
}

.role-custom-loading::after {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fade In Animasyonu */
.withdraw-form-container {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
