<?php
/**
 * Role Custom Para Çekme Sistemi Installer
 * 
 * @package Role_Custom
 * @subpackage Withdraw
 * @since 1.4.0
 */

// Dogrudan erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Para çekme sistemi veritabani tablolarini olusturan sinif
 */
class Role_Custom_Withdraw_Installer {
    
    /**
     * Veritabani tablolarini olustur
     */
    public static function create_tables() {
        global $wpdb;
        
        // WordPress veritabani upgrade fonksiyonunu yukle
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        // Charset ve collate ayarlarini al
        $charset_collate = $wpdb->get_charset_collate();
        
        // Para çekme talepleri tablosu
        self::create_withdraws_table($charset_collate);
        
        // Egitmen bakiye tablosu
        self::create_instructor_balance_table($charset_collate);
        
        // Para çekme islem gecmisi tablosu
        self::create_withdraw_transactions_table($charset_collate);

        // Guvenlik loglari tablosu
        self::create_security_logs_table($charset_collate);

        // Veritabani versiyonunu kaydet
        update_option('role_custom_withdraw_db_version', ROLE_CUSTOM_VERSION);
    }
    
    /**
     * Para çekme talepleri tablosunu olustur
     */
    private static function create_withdraws_table($charset_collate) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;
        
        $sql = "CREATE TABLE IF NOT EXISTS `{$table_name}` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint(20) unsigned NOT NULL,
            `amount` decimal(19,4) NOT NULL DEFAULT '0.0000',
            `method` varchar(50) NOT NULL DEFAULT 'bank',
            `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=pending, 1=approved, 2=cancelled, 3=processing',
            `note` text DEFAULT NULL,
            `admin_note` text DEFAULT NULL,
            `payment_details` longtext DEFAULT NULL,
            `charge_amount` decimal(19,4) NOT NULL DEFAULT '0.0000',
            `receivable_amount` decimal(19,4) NOT NULL DEFAULT '0.0000',
            `ip_address` varchar(45) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `processed_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `status` (`status`),
            KEY `method` (`method`),
            KEY `created_at` (`created_at`)
        ) {$charset_collate};";
        
        dbDelta($sql);
    }
    
    /**
     * Egitmen bakiye tablosunu olustur
     */
    private static function create_instructor_balance_table($charset_collate) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . ROLE_CUSTOM_INSTRUCTOR_BALANCE_TABLE;
        
        $sql = "CREATE TABLE IF NOT EXISTS `{$table_name}` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint(20) unsigned NOT NULL,
            `transaction_id` bigint(20) unsigned NOT NULL,
            `transaction_type` varchar(50) NOT NULL COMMENT 'course_sale, product_sale, commission, withdraw, refund',
            `description` text DEFAULT NULL,
            `debit` decimal(19,4) NOT NULL DEFAULT '0.0000',
            `credit` decimal(19,4) NOT NULL DEFAULT '0.0000',
            `balance` decimal(19,4) NOT NULL DEFAULT '0.0000',
            `reference_id` bigint(20) unsigned DEFAULT NULL COMMENT 'Order ID, Course ID, etc.',
            `reference_type` varchar(50) DEFAULT NULL COMMENT 'order, course, manual',
            `status` varchar(20) NOT NULL DEFAULT 'completed',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `transaction_type` (`transaction_type`),
            KEY `reference_id_type` (`reference_id`, `reference_type`),
            KEY `created_at` (`created_at`)
        ) {$charset_collate};";
        
        dbDelta($sql);
    }
    
    /**
     * Para çekme islem gecmisi tablosunu olustur
     */
    private static function create_withdraw_transactions_table($charset_collate) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TRANSACTIONS_TABLE;
        
        $sql = "CREATE TABLE IF NOT EXISTS `{$table_name}` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `withdraw_id` bigint(20) unsigned NOT NULL,
            `action` varchar(50) NOT NULL COMMENT 'created, approved, cancelled, processed',
            `performed_by` bigint(20) unsigned NOT NULL,
            `note` text DEFAULT NULL,
            `old_status` tinyint(1) DEFAULT NULL,
            `new_status` tinyint(1) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `withdraw_id` (`withdraw_id`),
            KEY `action` (`action`),
            KEY `performed_by` (`performed_by`),
            KEY `created_at` (`created_at`)
        ) {$charset_collate};";
        
        dbDelta($sql);
    }

    /**
     * Guvenlik loglari tablosunu olustur
     */
    private static function create_security_logs_table($charset_collate) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'role_custom_security_logs';

        $sql = "CREATE TABLE IF NOT EXISTS `{$table_name}` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint(20) unsigned NOT NULL,
            `activity_type` varchar(50) NOT NULL,
            `activity_data` longtext DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `activity_type` (`activity_type`),
            KEY `ip_address` (`ip_address`),
            KEY `created_at` (`created_at`)
        ) {$charset_collate};";

        dbDelta($sql);
    }

    /**
     * Tablolari sil (uninstall icin)
     */
    public static function drop_tables() {
        global $wpdb;
        
        $tables = [
            $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE,
            $wpdb->prefix . ROLE_CUSTOM_INSTRUCTOR_BALANCE_TABLE,
            $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TRANSACTIONS_TABLE,
            $wpdb->prefix . 'role_custom_security_logs'
        ];
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS `{$table}`");
        }
        
        // Veritabani versiyonunu sil
        delete_option('role_custom_withdraw_db_version');
    }
    
    /**
     * Veritabani guncellemesi gerekli mi kontrol et
     */
    public static function needs_update() {
        $current_version = get_option('role_custom_withdraw_db_version', '0.0.0');
        return version_compare($current_version, ROLE_CUSTOM_VERSION, '<');
    }
    
    /**
     * Varsayilan ayarlari olustur
     */
    public static function create_default_settings() {
        $default_settings = [
            'withdraw_methods' => ['bank', 'paypal'],
            'minimum_withdraw_amount' => 50,
            'withdraw_charge_fixed' => 0,
            'withdraw_charge_percentage' => 0,
            'auto_approve_withdraws' => false,
            'withdraw_schedule' => 'manual', // manual, weekly, monthly
            'payment_instructions' => [
                'bank' => 'Banka transferi ile odeme 1-3 is gunu icerisinde hesabiniza yatirilacaktir.',
                'paypal' => 'PayPal ile odeme 24 saat icerisinde hesabiniza yatirilacaktir.'
            ]
        ];
        
        add_option('role_custom_withdraw_settings', $default_settings);
    }
}
