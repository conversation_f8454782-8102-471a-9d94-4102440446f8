<?php
/**
 * Admin Para Çekme Talepleri Sayfası
 * 
 * @package Role_Custom
 * @subpackage Templates
 * @since 1.4.0
 */

// <PERSON><PERSON>an erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Para çekme yoneticisini al
$withdraw_manager = Role_Custom_Withdraw_Manager::get_instance();

// Sayfa parametrelerini al
$current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'pending';
$per_page = 20;
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$offset = ($current_page - 1) * $per_page;

// Durum kodlarini al
$status_map = [
    'pending' => Role_Custom_Withdraw_Manager::STATUS_PENDING,
    'approved' => Role_Custom_Withdraw_Manager::STATUS_APPROVED,
    'cancelled' => Role_Custom_Withdraw_Manager::STATUS_CANCELLED,
    'processing' => Role_Custom_Withdraw_Manager::STATUS_PROCESSING
];

$current_status = isset($status_map[$current_tab]) ? $status_map[$current_tab] : null;

// Para çekme taleplerini al
$withdraws = $withdraw_manager->get_all_withdraws($current_status, $per_page, $offset);

// Istatistikleri al
$stats = $withdraw_manager->get_withdraw_stats();

?>

<div class="wrap role-custom-withdraws">
    <h1 class="wp-heading-inline">
        <?php _e('Para Çekme Talepleri', 'role-custom'); ?>
    </h1>
    
    <!-- Istatistik Kartlari -->
    <div class="role-custom-stats-grid">
        <div class="role-custom-stat-card">
            <div class="stat-number"><?php echo number_format($stats['pending_requests']); ?></div>
            <div class="stat-label"><?php _e('Bekleyen Talepler', 'role-custom'); ?></div>
        </div>
        
        <div class="role-custom-stat-card">
            <div class="stat-number"><?php echo number_format($stats['approved_requests']); ?></div>
            <div class="stat-label"><?php _e('Onaylanan Talepler', 'role-custom'); ?></div>
        </div>
        
        <div class="role-custom-stat-card">
            <div class="stat-number"><?php echo wc_price($stats['total_pending_amount']); ?></div>
            <div class="stat-label"><?php _e('Bekleyen Tutar', 'role-custom'); ?></div>
        </div>
        
        <div class="role-custom-stat-card">
            <div class="stat-number"><?php echo wc_price($stats['total_approved_amount']); ?></div>
            <div class="stat-label"><?php _e('Onaylanan Tutar', 'role-custom'); ?></div>
        </div>
    </div>
    
    <!-- Tab Navigasyonu -->
    <nav class="nav-tab-wrapper wp-clearfix">
        <a href="<?php echo admin_url('admin.php?page=role-custom-withdraws&tab=pending'); ?>" 
           class="nav-tab <?php echo $current_tab === 'pending' ? 'nav-tab-active' : ''; ?>">
            <?php _e('Bekleyen', 'role-custom'); ?>
            <span class="count">(<?php echo $stats['pending_requests']; ?>)</span>
        </a>
        
        <a href="<?php echo admin_url('admin.php?page=role-custom-withdraws&tab=approved'); ?>" 
           class="nav-tab <?php echo $current_tab === 'approved' ? 'nav-tab-active' : ''; ?>">
            <?php _e('Onaylanan', 'role-custom'); ?>
            <span class="count">(<?php echo $stats['approved_requests']; ?>)</span>
        </a>
        
        <a href="<?php echo admin_url('admin.php?page=role-custom-withdraws&tab=cancelled'); ?>" 
           class="nav-tab <?php echo $current_tab === 'cancelled' ? 'nav-tab-active' : ''; ?>">
            <?php _e('İptal Edilen', 'role-custom'); ?>
            <span class="count">(<?php echo $stats['cancelled_requests']; ?>)</span>
        </a>
        
        <a href="<?php echo admin_url('admin.php?page=role-custom-withdraws&tab=processing'); ?>" 
           class="nav-tab <?php echo $current_tab === 'processing' ? 'nav-tab-active' : ''; ?>">
            <?php _e('İşleniyor', 'role-custom'); ?>
        </a>
    </nav>
    
    <!-- Para Çekme Talepleri Tablosu -->
    <div class="role-custom-withdraws-table">
        <?php if (empty($withdraws)): ?>
            <div class="role-custom-empty-state">
                <p><?php _e('Bu durumda hiç para çekme talebi bulunmuyor.', 'role-custom'); ?></p>
            </div>
        <?php else: ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col"><?php _e('ID', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Eğitmen', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Tutar', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Alacak Tutar', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Yöntem', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Tarih', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Durum', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('İşlemler', 'role-custom'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($withdraws as $withdraw): ?>
                        <tr>
                            <td><strong>#<?php echo $withdraw->id; ?></strong></td>
                            <td>
                                <strong><?php echo esc_html($withdraw->display_name); ?></strong><br>
                                <small><?php echo esc_html($withdraw->user_email); ?></small>
                            </td>
                            <td><?php echo wc_price($withdraw->amount); ?></td>
                            <td><?php echo wc_price($withdraw->receivable_amount); ?></td>
                            <td>
                                <?php 
                                $method_names = [
                                    'bank' => __('Banka Transferi', 'role-custom'),
                                    'paypal' => __('PayPal', 'role-custom')
                                ];
                                echo isset($method_names[$withdraw->method]) ? $method_names[$withdraw->method] : ucfirst($withdraw->method);
                                ?>
                            </td>
                            <td><?php echo date_i18n('d.m.Y H:i', strtotime($withdraw->created_at)); ?></td>
                            <td>
                                <?php
                                $status_labels = [
                                    Role_Custom_Withdraw_Manager::STATUS_PENDING => '<span class="status-pending">' . __('Bekliyor', 'role-custom') . '</span>',
                                    Role_Custom_Withdraw_Manager::STATUS_APPROVED => '<span class="status-approved">' . __('Onaylandı', 'role-custom') . '</span>',
                                    Role_Custom_Withdraw_Manager::STATUS_CANCELLED => '<span class="status-cancelled">' . __('İptal Edildi', 'role-custom') . '</span>',
                                    Role_Custom_Withdraw_Manager::STATUS_PROCESSING => '<span class="status-processing">' . __('İşleniyor', 'role-custom') . '</span>'
                                ];
                                echo isset($status_labels[$withdraw->status]) ? $status_labels[$withdraw->status] : __('Bilinmiyor', 'role-custom');
                                ?>
                            </td>
                            <td>
                                <div class="row-actions">
                                    <span class="view">
                                        <a href="#" class="view-withdraw" data-id="<?php echo $withdraw->id; ?>">
                                            <?php _e('Görüntüle', 'role-custom'); ?>
                                        </a>
                                    </span>
                                    
                                    <?php if ($withdraw->status == Role_Custom_Withdraw_Manager::STATUS_PENDING): ?>
                                        | <span class="approve">
                                            <a href="#" class="approve-withdraw" data-id="<?php echo $withdraw->id; ?>">
                                                <?php _e('Onayla', 'role-custom'); ?>
                                            </a>
                                        </span>
                                        | <span class="reject">
                                            <a href="#" class="reject-withdraw" data-id="<?php echo $withdraw->id; ?>">
                                                <?php _e('Reddet', 'role-custom'); ?>
                                            </a>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<!-- Para Çekme Detay Modal -->
<div id="withdraw-detail-modal" class="role-custom-modal" style="display: none;">
    <div class="role-custom-modal-content">
        <div class="role-custom-modal-header">
            <h3><?php _e('Para Çekme Talebi Detayları', 'role-custom'); ?></h3>
            <span class="role-custom-modal-close">&times;</span>
        </div>
        <div class="role-custom-modal-body">
            <div id="withdraw-detail-content">
                <!-- İçerik AJAX ile yüklenecek -->
            </div>
        </div>
        <div class="role-custom-modal-footer">
            <button type="button" class="button" id="close-modal"><?php _e('Kapat', 'role-custom'); ?></button>
        </div>
    </div>
</div>

<!-- Onay/Red Modal -->
<div id="withdraw-action-modal" class="role-custom-modal" style="display: none;">
    <div class="role-custom-modal-content">
        <div class="role-custom-modal-header">
            <h3 id="action-modal-title"><?php _e('Para Çekme Talebi', 'role-custom'); ?></h3>
            <span class="role-custom-modal-close">&times;</span>
        </div>
        <div class="role-custom-modal-body">
            <form id="withdraw-action-form">
                <input type="hidden" id="action-withdraw-id" name="withdraw_id" value="">
                <input type="hidden" id="action-type" name="action" value="">
                
                <div class="form-field">
                    <label for="admin-note"><?php _e('Admin Notu:', 'role-custom'); ?></label>
                    <textarea id="admin-note" name="admin_note" rows="4" class="widefat"></textarea>
                    <p class="description"><?php _e('Bu not eğitmene gönderilecektir.', 'role-custom'); ?></p>
                </div>
            </form>
        </div>
        <div class="role-custom-modal-footer">
            <button type="button" class="button" id="cancel-action"><?php _e('İptal', 'role-custom'); ?></button>
            <button type="button" class="button button-primary" id="confirm-action"><?php _e('Onayla', 'role-custom'); ?></button>
        </div>
    </div>
</div>
