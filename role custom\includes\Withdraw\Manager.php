<?php
/**
 * Role Custom Para Çekme Yöneticisi
 * 
 * @package Role_Custom
 * @subpackage Withdraw
 * @since 1.4.0
 */

// Dogrudan erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Para çekme islemlerini yoneten ana sinif
 */
class Role_Custom_Withdraw_Manager {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Para çekme durumlari
     */
    const STATUS_PENDING = 0;
    const STATUS_APPROVED = 1;
    const STATUS_CANCELLED = 2;
    const STATUS_PROCESSING = 3;
    
    /**
     * Odeme yontemleri
     */
    const METHOD_BANK = 'bank';
    const METHOD_PAYPAL = 'paypal';
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Hook'lari baslat
        $this->init_hooks();
    }
    
    /**
     * Hook'lari baslat
     */
    private function init_hooks() {
        // AJAX hook'lari
        add_action('wp_ajax_role_custom_submit_withdraw_request', [$this, 'ajax_submit_withdraw_request']);
        add_action('wp_ajax_role_custom_cancel_withdraw_request', [$this, 'ajax_cancel_withdraw_request']);
        add_action('wp_ajax_role_custom_approve_withdraw', [$this, 'ajax_approve_withdraw']);
        add_action('wp_ajax_role_custom_reject_withdraw', [$this, 'ajax_reject_withdraw']);
        add_action('wp_ajax_role_custom_get_withdraw_history', [$this, 'ajax_get_withdraw_history']);
        add_action('wp_ajax_role_custom_calculate_withdraw_charges', [$this, 'ajax_calculate_withdraw_charges']);
        add_action('wp_ajax_role_custom_get_withdraw_detail', [$this, 'ajax_get_withdraw_detail']);
        
        // Admin hook'lari
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);



        // Frontend hook'lari
        add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_scripts']);
        add_shortcode('role_custom_instructor_withdraw', [$this, 'instructor_withdraw_shortcode']);
    }
    
    /**
     * Egitmenin mevcut bakiyesini al
     */
    public function get_instructor_balance($user_id) {
        // BalanceManager'dan al
        $balance_manager = Role_Custom_Balance_Manager::get_instance();
        return $balance_manager->get_instructor_balance($user_id);
    }
    
    /**
     * Para çekme talebi olustur
     */
    public function create_withdraw_request($user_id, $amount, $method, $note = '', $payment_details = []) {
        global $wpdb;
        
        // Dogrulama
        $validation = $this->validate_withdraw_request($user_id, $amount, $method);
        if (is_wp_error($validation)) {
            // Basarisiz girisimleri logla
            do_action('role_custom_withdraw_request_failed', $user_id, $validation->get_error_code(), $validation->get_error_message());
            return $validation;
        }

        // Guvenlik kontrolu
        $security_check = apply_filters('role_custom_before_withdraw_request', true, $user_id, $amount);
        if (is_wp_error($security_check)) {
            do_action('role_custom_withdraw_request_failed', $user_id, $security_check->get_error_code(), $security_check->get_error_message());
            return $security_check;
        }
        
        // Ucret hesaplama
        $charges = $this->calculate_withdraw_charges($amount, $method);
        $receivable_amount = $amount - $charges;
        
        // Veritabanina ekle
        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;
        
        $result = $wpdb->insert(
            $table_name,
            [
                'user_id' => $user_id,
                'amount' => $amount,
                'method' => $method,
                'status' => self::STATUS_PENDING,
                'note' => $note,
                'payment_details' => maybe_serialize($payment_details),
                'charge_amount' => $charges,
                'receivable_amount' => $receivable_amount,
                'ip_address' => $this->get_user_ip(),
                'created_at' => current_time('mysql')
            ],
            [
                '%d', '%f', '%s', '%d', '%s', '%s', '%f', '%f', '%s', '%s'
            ]
        );
        
        if ($result === false) {
            return new WP_Error('db_error', 'Veritabani hatasi olustu.');
        }
        
        $withdraw_id = $wpdb->insert_id;
        
        // Islem gecmisine ekle
        $this->add_transaction_log($withdraw_id, 'created', $user_id, 'Para çekme talebi olusturuldu');

        // Guvenlik aktivitesini logla
        do_action('role_custom_withdraw_request_created', $withdraw_id, $user_id);

        // Email bildirimi gonder
        $this->send_withdraw_notification($withdraw_id, 'created');

        return $withdraw_id;
    }
    
    /**
     * Para çekme talebini dogrula
     */
    private function validate_withdraw_request($user_id, $amount, $method) {
        // Kullanici kontrolu
        if (!$this->is_instructor($user_id)) {
            return new WP_Error('invalid_user', 'Sadece egitmenler para çekme talebi olusturabilir.');
        }
        
        // Bekleyen talep kontrolu
        if ($this->has_pending_request($user_id)) {
            return new WP_Error('pending_request', 'Zaten bekleyen bir para çekme talebiniz var.');
        }
        
        // Minimum tutar kontrolu
        $settings = get_option('role_custom_withdraw_settings', []);
        $min_amount = isset($settings['minimum_withdraw_amount']) ? $settings['minimum_withdraw_amount'] : 50;
        
        if ($amount < $min_amount) {
            return new WP_Error('min_amount', sprintf('Minimum para çekme tutari %s TL\'dir.', $min_amount));
        }
        
        // Bakiye kontrolu
        $balance = $this->get_instructor_balance($user_id);
        if ($amount > $balance) {
            return new WP_Error('insufficient_balance', 'Yetersiz bakiye.');
        }
        
        // Odeme yontemi kontrolu
        $allowed_methods = isset($settings['withdraw_methods']) ? $settings['withdraw_methods'] : ['bank'];
        if (!in_array($method, $allowed_methods)) {
            return new WP_Error('invalid_method', 'Gecersiz odeme yontemi.');
        }
        
        return true;
    }
    
    /**
     * Para çekme ucretlerini hesapla
     */
    private function calculate_withdraw_charges($amount, $method) {
        $settings = get_option('role_custom_withdraw_settings', []);
        
        $fixed_charge = isset($settings['withdraw_charge_fixed']) ? $settings['withdraw_charge_fixed'] : 0;
        $percentage_charge = isset($settings['withdraw_charge_percentage']) ? $settings['withdraw_charge_percentage'] : 0;
        
        $total_charge = $fixed_charge + ($amount * $percentage_charge / 100);
        
        return $total_charge;
    }
    
    /**
     * Kullanicinin bekleyen talebi var mi kontrol et
     */
    public function has_pending_request($user_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;
        
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} 
             WHERE user_id = %d AND status = %d",
            $user_id,
            self::STATUS_PENDING
        ));
        
        return $count > 0;
    }
    
    /**
     * Kullanici egitmen mi kontrol et
     */
    private function is_instructor($user_id) {
        $user = get_userdata($user_id);
        return $user && in_array('tutor_instructor', $user->roles);
    }
    
    /**
     * Kullanicinin IP adresini al
     */
    private function get_user_ip() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
    
    /**
     * Islem gecmisine log ekle
     */
    private function add_transaction_log($withdraw_id, $action, $performed_by, $note = '', $old_status = null, $new_status = null) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TRANSACTIONS_TABLE;
        
        $wpdb->insert(
            $table_name,
            [
                'withdraw_id' => $withdraw_id,
                'action' => $action,
                'performed_by' => $performed_by,
                'note' => $note,
                'old_status' => $old_status,
                'new_status' => $new_status,
                'created_at' => current_time('mysql')
            ],
            [
                '%d', '%s', '%d', '%s', '%d', '%d', '%s'
            ]
        );
    }
    
    /**
     * Email bildirimi gonder
     */
    private function send_withdraw_notification($withdraw_id, $type) {
        // Email bildirim sistemi burada implement edilecek
        // Simdilik placeholder
        do_action('role_custom_withdraw_notification', $withdraw_id, $type);
    }

    /**
     * Para çekme talebini onayla
     */
    public function approve_withdraw($withdraw_id, $admin_note = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;

        // Mevcut talebi al
        $withdraw = $this->get_withdraw_by_id($withdraw_id);
        if (!$withdraw) {
            return new WP_Error('not_found', 'Para çekme talebi bulunamadi.');
        }

        // Sadece pending durumundaki talepler onaylanabilir
        if ($withdraw->status != self::STATUS_PENDING) {
            return new WP_Error('invalid_status', 'Sadece bekleyen talepler onaylanabilir.');
        }

        // Durumu guncelle
        $result = $wpdb->update(
            $table_name,
            [
                'status' => self::STATUS_APPROVED,
                'admin_note' => $admin_note,
                'processed_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ],
            ['id' => $withdraw_id],
            ['%d', '%s', '%s', '%s'],
            ['%d']
        );

        if ($result === false) {
            return new WP_Error('db_error', 'Veritabani hatasi olustu.');
        }

        // Bakiyeden dusur
        $this->deduct_from_balance($withdraw->user_id, $withdraw->amount, $withdraw_id);

        // Islem gecmisine ekle
        $this->add_transaction_log(
            $withdraw_id,
            'approved',
            get_current_user_id(),
            $admin_note,
            self::STATUS_PENDING,
            self::STATUS_APPROVED
        );

        // Email bildirimi gonder
        $this->send_withdraw_notification($withdraw_id, 'approved');

        return true;
    }

    /**
     * Para çekme talebini reddet
     */
    public function reject_withdraw($withdraw_id, $admin_note = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;

        // Mevcut talebi al
        $withdraw = $this->get_withdraw_by_id($withdraw_id);
        if (!$withdraw) {
            return new WP_Error('not_found', 'Para çekme talebi bulunamadi.');
        }

        // Sadece pending durumundaki talepler reddedilebilir
        if ($withdraw->status != self::STATUS_PENDING) {
            return new WP_Error('invalid_status', 'Sadece bekleyen talepler reddedilebilir.');
        }

        // Durumu guncelle
        $result = $wpdb->update(
            $table_name,
            [
                'status' => self::STATUS_CANCELLED,
                'admin_note' => $admin_note,
                'updated_at' => current_time('mysql')
            ],
            ['id' => $withdraw_id],
            ['%d', '%s', '%s'],
            ['%d']
        );

        if ($result === false) {
            return new WP_Error('db_error', 'Veritabani hatasi olustu.');
        }

        // Islem gecmisine ekle
        $this->add_transaction_log(
            $withdraw_id,
            'cancelled',
            get_current_user_id(),
            $admin_note,
            self::STATUS_PENDING,
            self::STATUS_CANCELLED
        );

        // Email bildirimi gonder
        $this->send_withdraw_notification($withdraw_id, 'cancelled');

        return true;
    }

    /**
     * Bakiyeden para dusur
     */
    private function deduct_from_balance($user_id, $amount, $withdraw_id) {
        $balance_manager = Role_Custom_Balance_Manager::get_instance();

        $result = $balance_manager->add_balance_transaction($user_id, [
            'transaction_id' => $withdraw_id,
            'transaction_type' => Role_Custom_Balance_Manager::TRANSACTION_WITHDRAW,
            'description' => 'Para çekme talebi onaylandi',
            'debit' => 0,
            'credit' => $amount,
            'reference_id' => $withdraw_id,
            'reference_type' => 'withdraw',
            'status' => 'completed'
        ]);

        return $result;
    }

    /**
     * ID ile para çekme talebini al
     */
    public function get_withdraw_by_id($withdraw_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE id = %d",
            $withdraw_id
        ));
    }

    /**
     * AJAX: Para çekme talebi gonder
     */
    public function ajax_submit_withdraw_request() {
        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_withdraw_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        // Kullanici kontrolu
        if (!$this->is_instructor(get_current_user_id())) {
            wp_send_json_error('Yetkisiz erisim.');
        }

        $amount = floatval($_POST['amount']);
        $method = sanitize_text_field($_POST['method']);
        $note = sanitize_textarea_field($_POST['note']);
        $payment_details = [];

        // Odeme detaylarini al
        if ($method === self::METHOD_BANK) {
            $payment_details = [
                'bank_name' => sanitize_text_field($_POST['bank_name']),
                'account_holder' => sanitize_text_field($_POST['account_holder']),
                'account_number' => sanitize_text_field($_POST['account_number']),
                'iban' => sanitize_text_field($_POST['iban'])
            ];
        } elseif ($method === self::METHOD_PAYPAL) {
            $payment_details = [
                'paypal_email' => sanitize_email($_POST['paypal_email'])
            ];
        }

        $result = $this->create_withdraw_request(
            get_current_user_id(),
            $amount,
            $method,
            $note,
            $payment_details
        );

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success('Para çekme talebiniz basariyla gonderildi.');
    }

    /**
     * AJAX: Para çekme talebini iptal et
     */
    public function ajax_cancel_withdraw_request() {
        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_withdraw_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $withdraw_id = intval($_POST['withdraw_id']);

        // Talep sahibi kontrolu
        $withdraw = $this->get_withdraw_by_id($withdraw_id);
        if (!$withdraw || $withdraw->user_id != get_current_user_id()) {
            wp_send_json_error('Yetkisiz erisim.');
        }

        if ($withdraw->status != self::STATUS_PENDING) {
            wp_send_json_error('Sadece bekleyen talepler iptal edilebilir.');
        }

        global $wpdb;
        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;

        $result = $wpdb->update(
            $table_name,
            ['status' => self::STATUS_CANCELLED, 'updated_at' => current_time('mysql')],
            ['id' => $withdraw_id],
            ['%d', '%s'],
            ['%d']
        );

        if ($result === false) {
            wp_send_json_error('Veritabani hatasi olustu.');
        }

        // Islem gecmisine ekle
        $this->add_transaction_log(
            $withdraw_id,
            'cancelled',
            get_current_user_id(),
            'Kullanici tarafindan iptal edildi',
            self::STATUS_PENDING,
            self::STATUS_CANCELLED
        );

        wp_send_json_success('Para çekme talebiniz iptal edildi.');
    }

    /**
     * AJAX: Para çekme talebini onayla (Admin)
     */
    public function ajax_approve_withdraw() {
        // Yetki kontrolu
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Yetkisiz erisim.');
        }

        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_admin_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $withdraw_id = intval($_POST['withdraw_id']);
        $admin_note = sanitize_textarea_field($_POST['admin_note']);

        $result = $this->approve_withdraw($withdraw_id, $admin_note);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success('Para çekme talebi onaylandi.');
    }

    /**
     * AJAX: Para çekme talebini reddet (Admin)
     */
    public function ajax_reject_withdraw() {
        // Yetki kontrolu
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Yetkisiz erisim.');
        }

        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_admin_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $withdraw_id = intval($_POST['withdraw_id']);
        $admin_note = sanitize_textarea_field($_POST['admin_note']);

        $result = $this->reject_withdraw($withdraw_id, $admin_note);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success('Para çekme talebi reddedildi.');
    }





    /**
     * Bekleyen para çekme talep sayisini al
     */
    public function get_pending_withdraws_count() {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE status = %d",
            self::STATUS_PENDING
        ));

        return intval($count);
    }

    /**
     * Admin para çekme talepleri sayfasi
     */
    public function admin_page_withdraws() {
        // Sayfa icerigini yukle
        include ROLE_CUSTOM_PLUGIN_DIR . 'templates/admin/withdraws.php';
    }

    /**
     * Admin egitmen bakiyeleri sayfasi
     */
    public function admin_page_instructor_balances() {
        // Sayfa icerigini yukle
        include ROLE_CUSTOM_PLUGIN_DIR . 'templates/admin/instructor-balances.php';
    }

    /**
     * Admin sistem testleri sayfasi
     */
    public function admin_page_system_tests() {
        // Sayfa icerigini yukle
        include ROLE_CUSTOM_PLUGIN_DIR . 'templates/admin/system-tests.php';
    }

    /**
     * Egitmen para çekme sayfasi
     */
    public function instructor_withdraw_page() {
        // Egitmen yetkisi kontrolu
        if (!$this->is_current_user_instructor()) {
            wp_die(__('Bu sayfaya erişim yetkiniz yok.', 'role-custom'));
        }

        // Sayfa icerigini yukle
        include ROLE_CUSTOM_PLUGIN_DIR . 'templates/instructor/withdraw-request.php';
    }

    /**
     * Egitmen talep gecmisi sayfasi
     */
    public function instructor_withdraw_history_page() {
        // Egitmen yetkisi kontrolu
        if (!$this->is_current_user_instructor()) {
            wp_die(__('Bu sayfaya erişim yetkiniz yok.', 'role-custom'));
        }

        // Sayfa icerigini yukle
        include ROLE_CUSTOM_PLUGIN_DIR . 'templates/instructor/withdraw-history.php';
    }

    /**
     * Egitmen bakiye durumu sayfasi
     */
    public function instructor_balance_status_page() {
        // Egitmen yetkisi kontrolu
        if (!$this->is_current_user_instructor()) {
            wp_die(__('Bu sayfaya erişim yetkiniz yok.', 'role-custom'));
        }

        // Sayfa icerigini yukle
        include ROLE_CUSTOM_PLUGIN_DIR . 'templates/instructor/balance-status.php';
    }

    /**
     * Admin script'lerini yukle
     */
    public function enqueue_admin_scripts($hook) {
        // Sadece para çekme, bakiye, test ve egitmen sayfalarinda yukle
        $allowed_pages = [
            'role-custom-withdraws',
            'role-custom-instructor-balances',
            'role-custom-system-tests',
            'instructor-withdraw',
            'instructor-withdraw-history',
            'instructor-balance-status'
        ];
        $is_allowed_page = false;

        foreach ($allowed_pages as $page) {
            if (strpos($hook, $page) !== false) {
                $is_allowed_page = true;
                break;
            }
        }

        if (!$is_allowed_page) {
            return;
        }

        wp_enqueue_script(
            'role-custom-withdraw-admin',
            ROLE_CUSTOM_PLUGIN_URL . 'assets/js/withdraw-admin.js',
            ['jquery'],
            ROLE_CUSTOM_VERSION,
            true
        );

        wp_enqueue_style(
            'role-custom-withdraw-admin',
            ROLE_CUSTOM_PLUGIN_URL . 'assets/css/withdraw-admin.css',
            [],
            ROLE_CUSTOM_VERSION
        );

        // AJAX verilerini gonder
        wp_localize_script('role-custom-withdraw-admin', 'roleCustomWithdraw', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('role_custom_admin_nonce'),
            'strings' => [
                'confirmApprove' => 'Bu para çekme talebini onaylamak istediginizden emin misiniz?',
                'confirmReject' => 'Bu para çekme talebini reddetmek istediginizden emin misiniz?',
                'processing' => 'Isleniyor...',
                'success' => 'Islem basariyla tamamlandi.',
                'error' => 'Bir hata olustu.'
            ]
        ]);
    }

    /**
     * Tum para çekme taleplerini al (Admin icin)
     */
    public function get_all_withdraws($status = null, $limit = 20, $offset = 0) {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;

        $where = '';
        $params = [];

        if ($status !== null) {
            $where = 'WHERE status = %d';
            $params[] = $status;
        }

        $params[] = $limit;
        $params[] = $offset;

        $query = "SELECT w.*, u.display_name, u.user_email
                  FROM {$table_name} w
                  LEFT JOIN {$wpdb->users} u ON w.user_id = u.ID
                  {$where}
                  ORDER BY w.created_at DESC
                  LIMIT %d OFFSET %d";

        return $wpdb->get_results($wpdb->prepare($query, $params));
    }

    /**
     * Para çekme istatistiklerini al
     */
    public function get_withdraw_stats() {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;

        $stats = $wpdb->get_row("
            SELECT
                COUNT(*) as total_requests,
                SUM(CASE WHEN status = " . self::STATUS_PENDING . " THEN 1 ELSE 0 END) as pending_requests,
                SUM(CASE WHEN status = " . self::STATUS_APPROVED . " THEN 1 ELSE 0 END) as approved_requests,
                SUM(CASE WHEN status = " . self::STATUS_CANCELLED . " THEN 1 ELSE 0 END) as cancelled_requests,
                SUM(CASE WHEN status = " . self::STATUS_APPROVED . " THEN amount ELSE 0 END) as total_approved_amount,
                SUM(CASE WHEN status = " . self::STATUS_PENDING . " THEN amount ELSE 0 END) as total_pending_amount
            FROM {$table_name}
        ", ARRAY_A);

        return $stats;
    }



    /**
     * AJAX: Para çekme gecmisini al
     */
    public function ajax_get_withdraw_history() {
        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_withdraw_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $user_id = get_current_user_id();
        $page = intval($_POST['page']) ?: 1;
        $limit = 10;
        $offset = ($page - 1) * $limit;

        $history = $this->get_user_withdraw_history($user_id, $limit, $offset);

        wp_send_json_success($history);
    }

    /**
     * AJAX: Ucret hesaplama
     */
    public function ajax_calculate_withdraw_charges() {
        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_withdraw_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $amount = floatval($_POST['amount']);
        $method = sanitize_text_field($_POST['method']);

        if ($amount <= 0) {
            wp_send_json_error('Gecersiz tutar.');
        }

        $charges = $this->calculate_withdraw_charges($amount, $method);
        $receivable = $amount - $charges;

        wp_send_json_success([
            'requested_amount' => $amount,
            'charge_amount' => $charges,
            'receivable_amount' => $receivable,
            'requested_amount_formatted' => wc_price($amount),
            'charge_amount_formatted' => wc_price($charges),
            'receivable_amount_formatted' => wc_price($receivable)
        ]);
    }

    /**
     * AJAX: Para çekme detayini al
     */
    public function ajax_get_withdraw_detail() {
        // Yetki kontrolu
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Yetkisiz erisim.');
        }

        // Nonce kontrolu
        if (!wp_verify_nonce($_POST['nonce'], 'role_custom_admin_nonce')) {
            wp_die('Guvenlik kontrolu basarisiz.');
        }

        $withdraw_id = intval($_POST['withdraw_id']);
        $withdraw = $this->get_withdraw_detail($withdraw_id);

        if (!$withdraw) {
            wp_send_json_error('Para çekme talebi bulunamadi.');
        }

        // HTML icerigini olustur
        $html = $this->generate_withdraw_detail_html($withdraw);

        wp_send_json_success(['html' => $html]);
    }

    /**
     * Para çekme detayini al (kullanici bilgileri ile)
     */
    public function get_withdraw_detail($withdraw_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;

        $withdraw = $wpdb->get_row($wpdb->prepare(
            "SELECT w.*, u.display_name, u.user_email, u.user_login
             FROM {$table_name} w
             LEFT JOIN {$wpdb->users} u ON w.user_id = u.ID
             WHERE w.id = %d",
            $withdraw_id
        ));

        if ($withdraw && $withdraw->payment_details) {
            $withdraw->payment_details = maybe_unserialize($withdraw->payment_details);
        }

        return $withdraw;
    }

    /**
     * Para çekme detay HTML'ini olustur
     */
    private function generate_withdraw_detail_html($withdraw) {
        $status_labels = [
            self::STATUS_PENDING => __('Bekliyor', 'role-custom'),
            self::STATUS_APPROVED => __('Onaylandi', 'role-custom'),
            self::STATUS_CANCELLED => __('Iptal Edildi', 'role-custom'),
            self::STATUS_PROCESSING => __('Isleniyor', 'role-custom')
        ];

        $method_names = [
            'bank' => __('Banka Transferi', 'role-custom'),
            'paypal' => __('PayPal', 'role-custom')
        ];

        ob_start();
        ?>
        <div class="withdraw-detail-grid">
            <div class="withdraw-detail-section">
                <h4><?php _e('Genel Bilgiler', 'role-custom'); ?></h4>
                <div class="withdraw-detail-item">
                    <span class="withdraw-detail-label"><?php _e('Talep ID:', 'role-custom'); ?></span>
                    <span class="withdraw-detail-value">#<?php echo $withdraw->id; ?></span>
                </div>
                <div class="withdraw-detail-item">
                    <span class="withdraw-detail-label"><?php _e('Egitmen:', 'role-custom'); ?></span>
                    <span class="withdraw-detail-value"><?php echo esc_html($withdraw->display_name); ?></span>
                </div>
                <div class="withdraw-detail-item">
                    <span class="withdraw-detail-label"><?php _e('E-posta:', 'role-custom'); ?></span>
                    <span class="withdraw-detail-value"><?php echo esc_html($withdraw->user_email); ?></span>
                </div>
                <div class="withdraw-detail-item">
                    <span class="withdraw-detail-label"><?php _e('Durum:', 'role-custom'); ?></span>
                    <span class="withdraw-detail-value"><?php echo isset($status_labels[$withdraw->status]) ? $status_labels[$withdraw->status] : __('Bilinmiyor', 'role-custom'); ?></span>
                </div>
            </div>

            <div class="withdraw-detail-section">
                <h4><?php _e('Tutar Bilgileri', 'role-custom'); ?></h4>
                <div class="withdraw-detail-item">
                    <span class="withdraw-detail-label"><?php _e('Talep Edilen:', 'role-custom'); ?></span>
                    <span class="withdraw-detail-value"><?php echo wc_price($withdraw->amount); ?></span>
                </div>
                <div class="withdraw-detail-item">
                    <span class="withdraw-detail-label"><?php _e('Islem Ucreti:', 'role-custom'); ?></span>
                    <span class="withdraw-detail-value"><?php echo wc_price($withdraw->charge_amount); ?></span>
                </div>
                <div class="withdraw-detail-item">
                    <span class="withdraw-detail-label"><?php _e('Alacak Tutar:', 'role-custom'); ?></span>
                    <span class="withdraw-detail-value"><strong><?php echo wc_price($withdraw->receivable_amount); ?></strong></span>
                </div>
                <div class="withdraw-detail-item">
                    <span class="withdraw-detail-label"><?php _e('Yontem:', 'role-custom'); ?></span>
                    <span class="withdraw-detail-value"><?php echo isset($method_names[$withdraw->method]) ? $method_names[$withdraw->method] : ucfirst($withdraw->method); ?></span>
                </div>
            </div>
        </div>

        <?php if ($withdraw->payment_details): ?>
        <div class="payment-details">
            <h4><?php _e('Odeme Detaylari', 'role-custom'); ?></h4>
            <?php if ($withdraw->method === 'bank' && is_array($withdraw->payment_details)): ?>
                <div class="detail-item">
                    <span class="detail-label"><?php _e('Banka:', 'role-custom'); ?></span>
                    <?php echo esc_html($withdraw->payment_details['bank_name'] ?? ''); ?>
                </div>
                <div class="detail-item">
                    <span class="detail-label"><?php _e('Hesap Sahibi:', 'role-custom'); ?></span>
                    <?php echo esc_html($withdraw->payment_details['account_holder'] ?? ''); ?>
                </div>
                <div class="detail-item">
                    <span class="detail-label"><?php _e('Hesap No:', 'role-custom'); ?></span>
                    <?php echo esc_html($withdraw->payment_details['account_number'] ?? ''); ?>
                </div>
                <div class="detail-item">
                    <span class="detail-label"><?php _e('IBAN:', 'role-custom'); ?></span>
                    <?php echo esc_html($withdraw->payment_details['iban'] ?? ''); ?>
                </div>
            <?php elseif ($withdraw->method === 'paypal' && is_array($withdraw->payment_details)): ?>
                <div class="detail-item">
                    <span class="detail-label"><?php _e('PayPal E-posta:', 'role-custom'); ?></span>
                    <?php echo esc_html($withdraw->payment_details['paypal_email'] ?? ''); ?>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <?php if ($withdraw->note): ?>
        <div class="payment-details">
            <h4><?php _e('Egitmen Notu', 'role-custom'); ?></h4>
            <p><?php echo esc_html($withdraw->note); ?></p>
        </div>
        <?php endif; ?>

        <?php if ($withdraw->admin_note): ?>
        <div class="payment-details">
            <h4><?php _e('Admin Notu', 'role-custom'); ?></h4>
            <p><?php echo esc_html($withdraw->admin_note); ?></p>
        </div>
        <?php endif; ?>

        <div class="payment-details">
            <h4><?php _e('Tarih Bilgileri', 'role-custom'); ?></h4>
            <div class="detail-item">
                <span class="detail-label"><?php _e('Olusturma:', 'role-custom'); ?></span>
                <?php echo date_i18n('d.m.Y H:i', strtotime($withdraw->created_at)); ?>
            </div>
            <?php if ($withdraw->updated_at && $withdraw->updated_at !== $withdraw->created_at): ?>
            <div class="detail-item">
                <span class="detail-label"><?php _e('Guncelleme:', 'role-custom'); ?></span>
                <?php echo date_i18n('d.m.Y H:i', strtotime($withdraw->updated_at)); ?>
            </div>
            <?php endif; ?>
            <?php if ($withdraw->processed_at): ?>
            <div class="detail-item">
                <span class="detail-label"><?php _e('Islenme:', 'role-custom'); ?></span>
                <?php echo date_i18n('d.m.Y H:i', strtotime($withdraw->processed_at)); ?>
            </div>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Frontend script'lerini yukle
     */
    public function enqueue_frontend_scripts() {
        // Sadece egitmen sayfalarinda yukle
        if (!is_user_logged_in() || !$this->is_instructor(get_current_user_id())) {
            return;
        }

        wp_enqueue_script(
            'role-custom-withdraw-instructor',
            ROLE_CUSTOM_PLUGIN_URL . 'assets/js/withdraw-instructor.js',
            ['jquery'],
            ROLE_CUSTOM_VERSION,
            true
        );

        wp_enqueue_style(
            'role-custom-withdraw-instructor',
            ROLE_CUSTOM_PLUGIN_URL . 'assets/css/withdraw-instructor.css',
            [],
            ROLE_CUSTOM_VERSION
        );

        // AJAX verilerini gonder
        wp_localize_script('role-custom-withdraw-instructor', 'roleCustomWithdraw', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('role_custom_withdraw_nonce'),
            'strings' => [
                'processing' => __('Isleniyor...', 'role-custom'),
                'success' => __('Islem basariyla tamamlandi.', 'role-custom'),
                'error' => __('Bir hata olustu.', 'role-custom'),
                'confirmCancel' => __('Para çekme talebinizi iptal etmek istediginizden emin misiniz?', 'role-custom')
            ]
        ]);
    }

    /**
     * Egitmen para çekme shortcode'u
     */
    public function instructor_withdraw_shortcode($atts) {
        // Sadece giris yapmis egitmenler gorebilir
        if (!is_user_logged_in()) {
            return '<p>' . __('Bu sayfayi gorebilmek icin giris yapmaniz gerekiyor.', 'role-custom') . '</p>';
        }

        if (!$this->is_instructor(get_current_user_id())) {
            return '<p>' . __('Bu sayfa sadece egitmenler icin erisilebilir.', 'role-custom') . '</p>';
        }

        // Template dosyasini yukle
        ob_start();
        include ROLE_CUSTOM_PLUGIN_DIR . 'templates/instructor/withdraw-dashboard.php';
        return ob_get_clean();
    }

    /**
     * Kullanicinin toplam para çekme talep sayisini al (filtreli)
     */
    public function get_user_withdraw_count($user_id, $status_filter = '', $method_filter = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;

        $where_conditions = ['user_id = %d'];
        $params = [$user_id];

        if ($status_filter) {
            $where_conditions[] = 'status = %d';
            $params[] = $status_filter;
        }

        if ($method_filter) {
            $where_conditions[] = 'method = %s';
            $params[] = $method_filter;
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        return $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} {$where_clause}",
            $params
        ));
    }



    /**
     * Kullanicinin para çekme gecmisini al (filtreli)
     */
    public function get_user_withdraw_history($user_id, $limit = 10, $offset = 0, $status_filter = '', $method_filter = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . ROLE_CUSTOM_WITHDRAW_TABLE;

        $where_conditions = ['user_id = %d'];
        $params = [$user_id];

        if ($status_filter) {
            $where_conditions[] = 'status = %d';
            $params[] = $status_filter;
        }

        if ($method_filter) {
            $where_conditions[] = 'method = %s';
            $params[] = $method_filter;
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        $params[] = $limit;
        $params[] = $offset;

        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$table_name}
             {$where_clause}
             ORDER BY created_at DESC
             LIMIT %d OFFSET %d",
            $params
        ));
    }
}
