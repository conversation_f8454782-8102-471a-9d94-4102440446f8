/**
 * Role Custom Para Çekme Admin JavaScript
 * 
 * @package Role_Custom
 * @subpackage Assets
 * @since 1.4.0
 */

(function($) {
    'use strict';

    // <PERSON>fa yüklendiğinde çalışacak fonksiyonlar
    $(document).ready(function() {
        initWithdrawAdmin();
    });

    /**
     * Para çekme admin panelini başlat
     */
    function initWithdrawAdmin() {
        // Modal event'lerini bağla
        bindModalEvents();
        
        // Tablo event'lerini bağla
        bindTableEvents();
        
        // Form event'lerini bağla
        bindFormEvents();
    }

    /**
     * Modal event'lerini bağla
     */
    function bindModalEvents() {
        // Modal kapatma
        $(document).on('click', '.role-custom-modal-close, #close-modal, #cancel-action', function() {
            closeModal();
        });

        // Modal dışına tıklayınca kapatma
        $(document).on('click', '.role-custom-modal', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // ESC tuşu ile kapatma
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27) { // ESC
                closeModal();
            }
        });
    }

    /**
     * Tablo event'lerini bağla
     */
    function bindTableEvents() {
        // Para çekme detayını görüntüle
        $(document).on('click', '.view-withdraw', function(e) {
            e.preventDefault();
            var withdrawId = $(this).data('id');
            viewWithdrawDetail(withdrawId);
        });

        // Para çekme talebini onayla
        $(document).on('click', '.approve-withdraw', function(e) {
            e.preventDefault();
            var withdrawId = $(this).data('id');
            showActionModal(withdrawId, 'approve');
        });

        // Para çekme talebini reddet
        $(document).on('click', '.reject-withdraw', function(e) {
            e.preventDefault();
            var withdrawId = $(this).data('id');
            showActionModal(withdrawId, 'reject');
        });
    }

    /**
     * Form event'lerini bağla
     */
    function bindFormEvents() {
        // Onay/Red işlemini gerçekleştir
        $(document).on('click', '#confirm-action', function() {
            var withdrawId = $('#action-withdraw-id').val();
            var action = $('#action-type').val();
            var adminNote = $('#admin-note').val();

            if (action === 'approve') {
                approveWithdraw(withdrawId, adminNote);
            } else if (action === 'reject') {
                rejectWithdraw(withdrawId, adminNote);
            }
        });
    }

    /**
     * Para çekme detayını görüntüle
     */
    function viewWithdrawDetail(withdrawId) {
        showLoading('#withdraw-detail-content');
        $('#withdraw-detail-modal').show();

        $.ajax({
            url: roleCustomWithdraw.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_get_withdraw_detail',
                withdraw_id: withdrawId,
                nonce: roleCustomWithdraw.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#withdraw-detail-content').html(response.data.html);
                } else {
                    showError('#withdraw-detail-content', response.data.message || roleCustomWithdraw.strings.error);
                }
            },
            error: function() {
                showError('#withdraw-detail-content', roleCustomWithdraw.strings.error);
            }
        });
    }

    /**
     * Onay/Red modalını göster
     */
    function showActionModal(withdrawId, action) {
        $('#action-withdraw-id').val(withdrawId);
        $('#action-type').val(action);
        $('#admin-note').val('');

        if (action === 'approve') {
            $('#action-modal-title').text('Para Çekme Talebini Onayla');
            $('#confirm-action').text('Onayla').removeClass('button-secondary').addClass('button-primary');
        } else {
            $('#action-modal-title').text('Para Çekme Talebini Reddet');
            $('#confirm-action').text('Reddet').removeClass('button-primary').addClass('button-secondary');
        }

        $('#withdraw-action-modal').show();
    }

    /**
     * Para çekme talebini onayla
     */
    function approveWithdraw(withdrawId, adminNote) {
        if (!confirm(roleCustomWithdraw.strings.confirmApprove)) {
            return;
        }

        setButtonLoading('#confirm-action', true);

        $.ajax({
            url: roleCustomWithdraw.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_approve_withdraw',
                withdraw_id: withdrawId,
                admin_note: adminNote,
                nonce: roleCustomWithdraw.nonce
            },
            success: function(response) {
                setButtonLoading('#confirm-action', false);
                
                if (response.success) {
                    showNotice(roleCustomWithdraw.strings.success, 'success');
                    closeModal();
                    // Sayfayı yenile
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showNotice(response.data.message || roleCustomWithdraw.strings.error, 'error');
                }
            },
            error: function() {
                setButtonLoading('#confirm-action', false);
                showNotice(roleCustomWithdraw.strings.error, 'error');
            }
        });
    }

    /**
     * Para çekme talebini reddet
     */
    function rejectWithdraw(withdrawId, adminNote) {
        if (!confirm(roleCustomWithdraw.strings.confirmReject)) {
            return;
        }

        setButtonLoading('#confirm-action', true);

        $.ajax({
            url: roleCustomWithdraw.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_reject_withdraw',
                withdraw_id: withdrawId,
                admin_note: adminNote,
                nonce: roleCustomWithdraw.nonce
            },
            success: function(response) {
                setButtonLoading('#confirm-action', false);
                
                if (response.success) {
                    showNotice(roleCustomWithdraw.strings.success, 'success');
                    closeModal();
                    // Sayfayı yenile
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showNotice(response.data.message || roleCustomWithdraw.strings.error, 'error');
                }
            },
            error: function() {
                setButtonLoading('#confirm-action', false);
                showNotice(roleCustomWithdraw.strings.error, 'error');
            }
        });
    }

    /**
     * Modal'ı kapat
     */
    function closeModal() {
        $('.role-custom-modal').hide();
    }

    /**
     * Loading durumunu göster
     */
    function showLoading(selector) {
        $(selector).html('<div class="role-custom-loading"></div>');
    }

    /**
     * Hata mesajını göster
     */
    function showError(selector, message) {
        $(selector).html('<div class="role-custom-notice notice-error"><p>' + message + '</p></div>');
    }

    /**
     * Bildirim göster
     */
    function showNotice(message, type) {
        var noticeClass = 'notice-' + type;
        var notice = '<div class="role-custom-notice ' + noticeClass + '"><p>' + message + '</p></div>';
        
        // Mevcut bildirimleri kaldır
        $('.role-custom-notice').remove();
        
        // Yeni bildirimi ekle
        $('.role-custom-withdraws').prepend(notice);
        
        // 5 saniye sonra kaldır
        setTimeout(function() {
            $('.role-custom-notice').fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    /**
     * Buton loading durumunu ayarla
     */
    function setButtonLoading(selector, loading) {
        var $button = $(selector);
        
        if (loading) {
            $button.prop('disabled', true);
            $button.data('original-text', $button.text());
            $button.text(roleCustomWithdraw.strings.processing);
        } else {
            $button.prop('disabled', false);
            $button.text($button.data('original-text') || $button.text());
        }
    }

})(jQuery);
