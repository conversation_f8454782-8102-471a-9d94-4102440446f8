<?php
/**
 * Eğitmen Para Çekme Talebi Sayfası
 * 
 * @package Role_Custom
 * @subpackage Templates
 * @since 1.4.0
 */

// Dogrudan erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Mevcut kullaniciyi al
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Bakiye yoneticisini al
$balance_manager = Role_Custom_Balance_Manager::get_instance();
$withdraw_manager = Role_Custom_Withdraw_Manager::get_instance();

// Mevcut bakiyeyi al
$current_balance = $balance_manager->get_instructor_balance($user_id);

// Minimum çekme tutari
$min_withdraw_amount = 50; // TL

// Son talepleri al
$recent_withdraws = $withdraw_manager->get_user_withdraw_history($user_id, 5);

?>

<div class="wrap instructor-withdraw-request">
    <h1 class="wp-heading-inline">
        <?php _e('Para Çekme Talebi', 'role-custom'); ?>
    </h1>
    
    <!-- Bakiye Bilgisi -->
    <div class="balance-info-card">
        <div class="balance-header">
            <h2><?php _e('Mevcut Bakiyeniz', 'role-custom'); ?></h2>
        </div>
        <div class="balance-amount">
            <span class="amount"><?php echo wc_price($current_balance); ?></span>
        </div>
        <div class="balance-status">
            <?php if ($current_balance >= $min_withdraw_amount): ?>
                <span class="status-available"><?php _e('Para çekme için yeterli bakiye', 'role-custom'); ?></span>
            <?php else: ?>
                <span class="status-insufficient"><?php printf(__('Minimum %s bakiye gerekli', 'role-custom'), wc_price($min_withdraw_amount)); ?></span>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if ($current_balance >= $min_withdraw_amount): ?>
        <!-- Para Çekme Formu -->
        <div class="withdraw-form-container">
            <h2><?php _e('Para Çekme Talebi Oluştur', 'role-custom'); ?></h2>
            
            <form id="instructor-withdraw-form" method="post">
                <?php wp_nonce_field('instructor_withdraw_request', 'withdraw_nonce'); ?>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="withdraw-amount"><?php _e('Çekmek İstediğiniz Tutar', 'role-custom'); ?> <span class="required">*</span></label>
                        <div class="amount-input-wrapper">
                            <input type="number" 
                                   id="withdraw-amount" 
                                   name="amount" 
                                   min="<?php echo $min_withdraw_amount; ?>" 
                                   max="<?php echo $current_balance; ?>" 
                                   step="0.01" 
                                   required>
                            <span class="currency-symbol">₺</span>
                        </div>
                        <small class="form-help"><?php printf(__('Minimum: %s - Maksimum: %s', 'role-custom'), wc_price($min_withdraw_amount), wc_price($current_balance)); ?></small>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="payment-method"><?php _e('Ödeme Yöntemi', 'role-custom'); ?> <span class="required">*</span></label>
                        <select id="payment-method" name="method" required>
                            <option value=""><?php _e('Ödeme yöntemi seçin...', 'role-custom'); ?></option>
                            <option value="bank"><?php _e('Banka Transferi', 'role-custom'); ?></option>
                            <option value="paypal"><?php _e('PayPal', 'role-custom'); ?></option>
                        </select>
                    </div>
                </div>
                
                <!-- Banka Bilgileri -->
                <div id="bank-details" class="payment-details" style="display: none;">
                    <h3><?php _e('Banka Bilgileri', 'role-custom'); ?></h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="bank-name"><?php _e('Banka Adı', 'role-custom'); ?></label>
                            <input type="text" id="bank-name" name="bank_details[bank_name]" placeholder="<?php _e('Örn: Türkiye İş Bankası', 'role-custom'); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="account-holder"><?php _e('Hesap Sahibi', 'role-custom'); ?></label>
                            <input type="text" id="account-holder" name="bank_details[account_holder]" value="<?php echo esc_attr($current_user->display_name); ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="iban"><?php _e('IBAN', 'role-custom'); ?></label>
                            <input type="text" id="iban" name="bank_details[iban]" placeholder="TR00 0000 0000 0000 0000 0000 00" maxlength="32">
                        </div>
                        
                        <div class="form-group">
                            <label for="account-number"><?php _e('Hesap Numarası', 'role-custom'); ?></label>
                            <input type="text" id="account-number" name="bank_details[account_number]" placeholder="<?php _e('Hesap numaranız', 'role-custom'); ?>">
                        </div>
                    </div>
                </div>
                
                <!-- PayPal Bilgileri -->
                <div id="paypal-details" class="payment-details" style="display: none;">
                    <h3><?php _e('PayPal Bilgileri', 'role-custom'); ?></h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="paypal-email"><?php _e('PayPal E-posta Adresi', 'role-custom'); ?></label>
                            <input type="email" id="paypal-email" name="paypal_details[email]" value="<?php echo esc_attr($current_user->user_email); ?>">
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="withdraw-note"><?php _e('Not (İsteğe Bağlı)', 'role-custom'); ?></label>
                        <textarea id="withdraw-note" name="note" rows="3" placeholder="<?php _e('Talep ile ilgili notunuz...', 'role-custom'); ?>"></textarea>
                    </div>
                </div>
                
                <!-- Ücret Bilgisi -->
                <div class="fee-info">
                    <h3><?php _e('Ücret Bilgisi', 'role-custom'); ?></h3>
                    <div class="fee-calculation">
                        <div class="fee-row">
                            <span class="fee-label"><?php _e('Talep Edilen Tutar:', 'role-custom'); ?></span>
                            <span class="fee-value" id="requested-amount">₺0.00</span>
                        </div>
                        <div class="fee-row">
                            <span class="fee-label"><?php _e('İşlem Ücreti:', 'role-custom'); ?></span>
                            <span class="fee-value" id="processing-fee">₺0.00</span>
                        </div>
                        <div class="fee-row total">
                            <span class="fee-label"><?php _e('Alacağınız Tutar:', 'role-custom'); ?></span>
                            <span class="fee-value" id="receivable-amount">₺0.00</span>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="button button-primary button-large">
                        <?php _e('Para Çekme Talebi Gönder', 'role-custom'); ?>
                    </button>
                    <button type="button" class="button button-secondary" id="preview-request">
                        <?php _e('Önizleme', 'role-custom'); ?>
                    </button>
                </div>
            </form>
        </div>
    <?php else: ?>
        <!-- Yetersiz Bakiye Mesajı -->
        <div class="insufficient-balance-notice">
            <h2><?php _e('Yetersiz Bakiye', 'role-custom'); ?></h2>
            <p><?php printf(__('Para çekme talebi oluşturmak için en az %s bakiyeniz olması gerekiyor.', 'role-custom'), wc_price($min_withdraw_amount)); ?></p>
            <p><?php _e('Daha fazla kurs satışı yaparak bakiyenizi artırabilirsiniz.', 'role-custom'); ?></p>
        </div>
    <?php endif; ?>
    
    <!-- Son Talepler -->
    <?php if (!empty($recent_withdraws)): ?>
        <div class="recent-withdraws">
            <h2><?php _e('Son Para Çekme Talepleri', 'role-custom'); ?></h2>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col"><?php _e('Tarih', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Tutar', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Yöntem', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Durum', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('İşlemler', 'role-custom'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_withdraws as $withdraw): ?>
                        <tr>
                            <td><?php echo date_i18n('d.m.Y H:i', strtotime($withdraw->created_at)); ?></td>
                            <td><?php echo wc_price($withdraw->amount); ?></td>
                            <td>
                                <?php 
                                $methods = [
                                    'bank' => __('Banka Transferi', 'role-custom'),
                                    'paypal' => __('PayPal', 'role-custom')
                                ];
                                echo $methods[$withdraw->method] ?? $withdraw->method;
                                ?>
                            </td>
                            <td>
                                <?php
                                $statuses = [
                                    Role_Custom_Withdraw_Manager::STATUS_PENDING => ['label' => __('Bekliyor', 'role-custom'), 'class' => 'pending'],
                                    Role_Custom_Withdraw_Manager::STATUS_APPROVED => ['label' => __('Onaylandı', 'role-custom'), 'class' => 'approved'],
                                    Role_Custom_Withdraw_Manager::STATUS_REJECTED => ['label' => __('Reddedildi', 'role-custom'), 'class' => 'rejected'],
                                    Role_Custom_Withdraw_Manager::STATUS_CANCELLED => ['label' => __('İptal Edildi', 'role-custom'), 'class' => 'cancelled'],
                                    Role_Custom_Withdraw_Manager::STATUS_COMPLETED => ['label' => __('Tamamlandı', 'role-custom'), 'class' => 'completed']
                                ];
                                $status = $statuses[$withdraw->status] ?? ['label' => $withdraw->status, 'class' => 'unknown'];
                                ?>
                                <span class="status-badge status-<?php echo $status['class']; ?>">
                                    <?php echo $status['label']; ?>
                                </span>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=instructor-withdraw-history&view=' . $withdraw->id); ?>" class="button button-small">
                                    <?php _e('Detay', 'role-custom'); ?>
                                </a>
                                <?php if ($withdraw->status == Role_Custom_Withdraw_Manager::STATUS_PENDING): ?>
                                    <button type="button" class="button button-small cancel-withdraw" data-id="<?php echo $withdraw->id; ?>">
                                        <?php _e('İptal', 'role-custom'); ?>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <p class="view-all">
                <a href="<?php echo admin_url('admin.php?page=instructor-withdraw-history'); ?>" class="button">
                    <?php _e('Tüm Talepleri Görüntüle', 'role-custom'); ?>
                </a>
            </p>
        </div>
    <?php endif; ?>
</div>

<style>
.instructor-withdraw-request {
    max-width: 1200px;
}

.balance-info-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.balance-amount .amount {
    font-size: 2.5em;
    font-weight: bold;
    color: #2271b1;
}

.status-available {
    color: #00a32a;
    font-weight: bold;
}

.status-insufficient {
    color: #d63638;
    font-weight: bold;
}

.withdraw-form-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row:last-child {
    grid-template-columns: 1fr;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: bold;
    margin-bottom: 5px;
}

.required {
    color: #d63638;
}

.amount-input-wrapper {
    position: relative;
}

.currency-symbol {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #646970;
}

.form-help {
    color: #646970;
    font-size: 12px;
    margin-top: 5px;
}

.payment-details {
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
    background: #f6f7f7;
}

.fee-info {
    background: #f0f6fc;
    border: 1px solid #2271b1;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.fee-calculation {
    margin-top: 10px;
}

.fee-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.fee-row.total {
    border-top: 1px solid #2271b1;
    padding-top: 5px;
    font-weight: bold;
    font-size: 1.1em;
}

.form-actions {
    text-align: center;
    margin-top: 30px;
}

.form-actions .button {
    margin: 0 10px;
}

.insufficient-balance-notice {
    background: #fcf9e8;
    border: 1px solid #dba617;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.recent-withdraws {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background: #fcf9e8;
    color: #dba617;
}

.status-approved {
    background: #f0f8ff;
    color: #2271b1;
}

.status-rejected {
    background: #fef7f7;
    color: #d63638;
}

.status-cancelled {
    background: #f6f7f7;
    color: #646970;
}

.status-completed {
    background: #f0f8f0;
    color: #00a32a;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Ödeme yöntemi değiştiğinde detayları göster/gizle
    $('#payment-method').change(function() {
        var method = $(this).val();
        $('.payment-details').hide();
        
        if (method === 'bank') {
            $('#bank-details').show();
            $('#bank-details input').attr('required', true);
            $('#paypal-details input').attr('required', false);
        } else if (method === 'paypal') {
            $('#paypal-details').show();
            $('#paypal-details input').attr('required', true);
            $('#bank-details input').attr('required', false);
        } else {
            $('.payment-details input').attr('required', false);
        }
    });
    
    // Tutar değiştiğinde ücret hesapla
    $('#withdraw-amount, #payment-method').on('change keyup', function() {
        calculateFees();
    });
    
    function calculateFees() {
        var amount = parseFloat($('#withdraw-amount').val()) || 0;
        var method = $('#payment-method').val();
        
        if (amount > 0 && method) {
            // AJAX ile ücret hesapla
            $.post(ajaxurl, {
                action: 'role_custom_calculate_withdraw_charges',
                amount: amount,
                method: method,
                nonce: '<?php echo wp_create_nonce("calculate_charges"); ?>'
            }, function(response) {
                if (response.success) {
                    $('#requested-amount').text('₺' + amount.toFixed(2));
                    $('#processing-fee').text('₺' + response.data.charges.toFixed(2));
                    $('#receivable-amount').text('₺' + response.data.receivable.toFixed(2));
                }
            });
        } else {
            $('#requested-amount').text('₺0.00');
            $('#processing-fee').text('₺0.00');
            $('#receivable-amount').text('₺0.00');
        }
    }
    
    // Form gönderimi
    $('#instructor-withdraw-form').submit(function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        formData += '&action=role_custom_submit_withdraw_request';
        
        $.post(ajaxurl, formData, function(response) {
            if (response.success) {
                alert('Para çekme talebiniz başarıyla gönderildi!');
                location.reload();
            } else {
                alert('Hata: ' + response.data);
            }
        });
    });
    
    // Talep iptal etme
    $('.cancel-withdraw').click(function() {
        var withdrawId = $(this).data('id');
        
        if (confirm('Bu talebi iptal etmek istediğinizden emin misiniz?')) {
            $.post(ajaxurl, {
                action: 'role_custom_cancel_withdraw_request',
                withdraw_id: withdrawId,
                nonce: '<?php echo wp_create_nonce("cancel_withdraw"); ?>'
            }, function(response) {
                if (response.success) {
                    alert('Talep başarıyla iptal edildi.');
                    location.reload();
                } else {
                    alert('Hata: ' + response.data);
                }
            });
        }
    });
});
</script>
