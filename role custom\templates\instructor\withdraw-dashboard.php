<?php
/**
 * Eğitmen Para Çekme Dashboard Template
 * 
 * @package Role_Custom
 * @subpackage Templates
 * @since 1.4.0
 */

// <PERSON>rudan erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Sadece eğitmenler erişebilir
if (!in_array('tutor_instructor', wp_get_current_user()->roles)) {
    wp_die(__('Bu sayfaya erişim yetkiniz yok.', 'role-custom'));
}

// Para çekme yoneticisini al
$withdraw_manager = Role_Custom_Withdraw_Manager::get_instance();
$current_user_id = get_current_user_id();

// Kullanicinin bakiyesini al
$current_balance = $withdraw_manager->get_instructor_balance($current_user_id);

// Para çekme ayarlarini al
$settings = get_option('role_custom_withdraw_settings', []);
$min_amount = isset($settings['minimum_withdraw_amount']) ? $settings['minimum_withdraw_amount'] : 50;
$allowed_methods = isset($settings['withdraw_methods']) ? $settings['withdraw_methods'] : ['bank'];

// Kullanicinin para çekme gecmisini al
$withdraw_history = $withdraw_manager->get_user_withdraw_history($current_user_id, 10);

// Bekleyen talep var mi kontrol et
$has_pending = $withdraw_manager->has_pending_request($current_user_id);

?>

<div class="role-custom-instructor-withdraw">
    <div class="withdraw-header">
        <h2><?php _e('Para Çekme', 'role-custom'); ?></h2>
        <p class="description"><?php _e('Kazancınızı çekebilir ve para çekme geçmişinizi görüntüleyebilirsiniz.', 'role-custom'); ?></p>
    </div>

    <!-- Bakiye Kartı -->
    <div class="balance-card">
        <div class="balance-info">
            <h3><?php _e('Mevcut Bakiye', 'role-custom'); ?></h3>
            <div class="balance-amount"><?php echo wc_price($current_balance); ?></div>
            <p class="balance-note">
                <?php printf(__('Minimum çekme tutarı: %s', 'role-custom'), wc_price($min_amount)); ?>
            </p>
        </div>
        
        <div class="balance-actions">
            <?php if ($current_balance >= $min_amount && !$has_pending): ?>
                <button type="button" class="button button-primary" id="new-withdraw-btn">
                    <?php _e('Para Çek', 'role-custom'); ?>
                </button>
            <?php elseif ($has_pending): ?>
                <button type="button" class="button" disabled>
                    <?php _e('Bekleyen Talebiniz Var', 'role-custom'); ?>
                </button>
            <?php else: ?>
                <button type="button" class="button" disabled>
                    <?php _e('Yetersiz Bakiye', 'role-custom'); ?>
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- Para Çekme Formu -->
    <div id="withdraw-form-container" class="withdraw-form-container" style="display: none;">
        <div class="withdraw-form-card">
            <h3><?php _e('Para Çekme Talebi Oluştur', 'role-custom'); ?></h3>
            
            <form id="withdraw-form">
                <?php wp_nonce_field('role_custom_withdraw_nonce', 'withdraw_nonce'); ?>
                
                <div class="form-row">
                    <label for="withdraw-amount"><?php _e('Çekilecek Tutar', 'role-custom'); ?></label>
                    <div class="amount-input-group">
                        <input type="number" 
                               id="withdraw-amount" 
                               name="amount" 
                               min="<?php echo $min_amount; ?>" 
                               max="<?php echo $current_balance; ?>" 
                               step="0.01" 
                               required>
                        <span class="currency-symbol"><?php echo get_woocommerce_currency_symbol(); ?></span>
                    </div>
                    <small class="form-help">
                        <?php printf(__('Minimum: %s - Maksimum: %s', 'role-custom'), 
                                   wc_price($min_amount), 
                                   wc_price($current_balance)); ?>
                    </small>
                </div>

                <div class="form-row">
                    <label for="withdraw-method"><?php _e('Ödeme Yöntemi', 'role-custom'); ?></label>
                    <select id="withdraw-method" name="method" required>
                        <option value=""><?php _e('Seçiniz...', 'role-custom'); ?></option>
                        <?php foreach ($allowed_methods as $method): ?>
                            <option value="<?php echo esc_attr($method); ?>">
                                <?php 
                                $method_names = [
                                    'bank' => __('Banka Transferi', 'role-custom'),
                                    'paypal' => __('PayPal', 'role-custom')
                                ];
                                echo isset($method_names[$method]) ? $method_names[$method] : ucfirst($method);
                                ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Banka Bilgileri -->
                <div id="bank-details" class="payment-details" style="display: none;">
                    <h4><?php _e('Banka Bilgileri', 'role-custom'); ?></h4>
                    
                    <div class="form-row">
                        <label for="bank-name"><?php _e('Banka Adı', 'role-custom'); ?></label>
                        <input type="text" id="bank-name" name="bank_name" placeholder="<?php _e('Örn: Ziraat Bankası', 'role-custom'); ?>">
                    </div>
                    
                    <div class="form-row">
                        <label for="account-holder"><?php _e('Hesap Sahibi', 'role-custom'); ?></label>
                        <input type="text" id="account-holder" name="account_holder" placeholder="<?php _e('Ad Soyad', 'role-custom'); ?>">
                    </div>
                    
                    <div class="form-row">
                        <label for="account-number"><?php _e('Hesap Numarası', 'role-custom'); ?></label>
                        <input type="text" id="account-number" name="account_number" placeholder="<?php _e('Hesap numaranız', 'role-custom'); ?>">
                    </div>
                    
                    <div class="form-row">
                        <label for="iban"><?php _e('IBAN', 'role-custom'); ?></label>
                        <input type="text" id="iban" name="iban" placeholder="TR00 0000 0000 0000 0000 0000 00">
                    </div>
                </div>

                <!-- PayPal Bilgileri -->
                <div id="paypal-details" class="payment-details" style="display: none;">
                    <h4><?php _e('PayPal Bilgileri', 'role-custom'); ?></h4>
                    
                    <div class="form-row">
                        <label for="paypal-email"><?php _e('PayPal E-posta', 'role-custom'); ?></label>
                        <input type="email" id="paypal-email" name="paypal_email" placeholder="<?php _e('<EMAIL>', 'role-custom'); ?>">
                    </div>
                </div>

                <div class="form-row">
                    <label for="withdraw-note"><?php _e('Not (Opsiyonel)', 'role-custom'); ?></label>
                    <textarea id="withdraw-note" name="note" rows="3" placeholder="<?php _e('Ek bilgiler...', 'role-custom'); ?>"></textarea>
                </div>

                <!-- Ücret Bilgisi -->
                <div id="charge-info" class="charge-info" style="display: none;">
                    <div class="charge-breakdown">
                        <div class="charge-row">
                            <span><?php _e('Talep Edilen Tutar:', 'role-custom'); ?></span>
                            <span id="requested-amount">-</span>
                        </div>
                        <div class="charge-row">
                            <span><?php _e('İşlem Ücreti:', 'role-custom'); ?></span>
                            <span id="charge-amount">-</span>
                        </div>
                        <div class="charge-row total">
                            <span><?php _e('Alacağınız Tutar:', 'role-custom'); ?></span>
                            <span id="receivable-amount">-</span>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="button" id="cancel-withdraw"><?php _e('İptal', 'role-custom'); ?></button>
                    <button type="submit" class="button button-primary" id="submit-withdraw"><?php _e('Talep Gönder', 'role-custom'); ?></button>
                </div>
            </form>
        </div>
    </div>

    <!-- Para Çekme Geçmişi -->
    <div class="withdraw-history">
        <h3><?php _e('Para Çekme Geçmişi', 'role-custom'); ?></h3>
        
        <?php if (empty($withdraw_history)): ?>
            <div class="empty-state">
                <p><?php _e('Henüz para çekme talebiniz bulunmuyor.', 'role-custom'); ?></p>
            </div>
        <?php else: ?>
            <div class="history-table">
                <table class="withdraw-history-table">
                    <thead>
                        <tr>
                            <th><?php _e('Tarih', 'role-custom'); ?></th>
                            <th><?php _e('Tutar', 'role-custom'); ?></th>
                            <th><?php _e('Yöntem', 'role-custom'); ?></th>
                            <th><?php _e('Durum', 'role-custom'); ?></th>
                            <th><?php _e('İşlemler', 'role-custom'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($withdraw_history as $withdraw): ?>
                            <tr>
                                <td><?php echo date_i18n('d.m.Y', strtotime($withdraw->created_at)); ?></td>
                                <td><?php echo wc_price($withdraw->amount); ?></td>
                                <td>
                                    <?php 
                                    $method_names = [
                                        'bank' => __('Banka', 'role-custom'),
                                        'paypal' => __('PayPal', 'role-custom')
                                    ];
                                    echo isset($method_names[$withdraw->method]) ? $method_names[$withdraw->method] : ucfirst($withdraw->method);
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    $status_labels = [
                                        Role_Custom_Withdraw_Manager::STATUS_PENDING => '<span class="status-pending">' . __('Bekliyor', 'role-custom') . '</span>',
                                        Role_Custom_Withdraw_Manager::STATUS_APPROVED => '<span class="status-approved">' . __('Onaylandı', 'role-custom') . '</span>',
                                        Role_Custom_Withdraw_Manager::STATUS_CANCELLED => '<span class="status-cancelled">' . __('İptal Edildi', 'role-custom') . '</span>',
                                        Role_Custom_Withdraw_Manager::STATUS_PROCESSING => '<span class="status-processing">' . __('İşleniyor', 'role-custom') . '</span>'
                                    ];
                                    echo isset($status_labels[$withdraw->status]) ? $status_labels[$withdraw->status] : __('Bilinmiyor', 'role-custom');
                                    ?>
                                </td>
                                <td>
                                    <button type="button" class="button-link view-details" data-id="<?php echo $withdraw->id; ?>">
                                        <?php _e('Detay', 'role-custom'); ?>
                                    </button>
                                    
                                    <?php if ($withdraw->status == Role_Custom_Withdraw_Manager::STATUS_PENDING): ?>
                                        | <button type="button" class="button-link cancel-request" data-id="<?php echo $withdraw->id; ?>">
                                            <?php _e('İptal Et', 'role-custom'); ?>
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>
