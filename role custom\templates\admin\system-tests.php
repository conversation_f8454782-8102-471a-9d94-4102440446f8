<?php
/**
 * Admin Sistem Testleri Sayfası
 * 
 * @package Role_Custom
 * @subpackage Templates
 * @since 1.4.0
 */

// Dogrudan erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Test sinifini yukle
require_once ROLE_CUSTOM_PLUGIN_DIR . 'includes/Withdraw/Tests.php';

$test_runner = new Role_Custom_Withdraw_Tests();

// Test calistirma istegi
if (isset($_POST['run_tests']) && wp_verify_nonce($_POST['test_nonce'], 'role_custom_run_tests')) {
    $test_report = $test_runner->generate_test_report();
    $performance_report = $test_runner->run_performance_test();
}

?>

<div class="wrap role-custom-system-tests">
    <h1 class="wp-heading-inline">
        <?php _e('Sistem Testleri', 'role-custom'); ?>
    </h1>
    
    <p class="description">
        <?php _e('Para çekme sisteminin doğru çalışıp çalışmadığını kontrol etmek için testleri çalıştırın.', 'role-custom'); ?>
    </p>
    
    <!-- Test Calistirma Formu -->
    <div class="test-controls">
        <form method="post" action="">
            <?php wp_nonce_field('role_custom_run_tests', 'test_nonce'); ?>
            <input type="submit" name="run_tests" class="button button-primary" value="<?php _e('Testleri Çalıştır', 'role-custom'); ?>">
        </form>
    </div>
    
    <?php if (isset($test_report)): ?>
        <!-- Test Sonuclari -->
        <div class="test-results">
            <h2><?php _e('Test Sonuçları', 'role-custom'); ?></h2>
            
            <!-- Ozet -->
            <div class="test-summary">
                <div class="summary-cards">
                    <div class="summary-card <?php echo $test_report['summary']['failed'] == 0 ? 'success' : 'warning'; ?>">
                        <div class="card-number"><?php echo $test_report['summary']['total']; ?></div>
                        <div class="card-label"><?php _e('Toplam Test', 'role-custom'); ?></div>
                    </div>
                    
                    <div class="summary-card success">
                        <div class="card-number"><?php echo $test_report['summary']['passed']; ?></div>
                        <div class="card-label"><?php _e('Başarılı', 'role-custom'); ?></div>
                    </div>
                    
                    <div class="summary-card <?php echo $test_report['summary']['failed'] > 0 ? 'error' : 'success'; ?>">
                        <div class="card-number"><?php echo $test_report['summary']['failed']; ?></div>
                        <div class="card-label"><?php _e('Başarısız', 'role-custom'); ?></div>
                    </div>
                    
                    <div class="summary-card <?php echo $test_report['summary']['success_rate'] >= 90 ? 'success' : ($test_report['summary']['success_rate'] >= 70 ? 'warning' : 'error'); ?>">
                        <div class="card-number"><?php echo $test_report['summary']['success_rate']; ?>%</div>
                        <div class="card-label"><?php _e('Başarı Oranı', 'role-custom'); ?></div>
                    </div>
                </div>
            </div>
            
            <!-- Detayli Sonuclar -->
            <div class="detailed-results">
                <h3><?php _e('Detaylı Sonuçlar', 'role-custom'); ?></h3>
                
                <?php
                $categories = [];
                foreach ($test_report['results'] as $result) {
                    $categories[$result['category']][] = $result;
                }
                ?>
                
                <?php foreach ($categories as $category => $tests): ?>
                    <div class="test-category">
                        <h4><?php echo esc_html($category); ?> <?php _e('Testleri', 'role-custom'); ?></h4>
                        
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th scope="col"><?php _e('Test', 'role-custom'); ?></th>
                                    <th scope="col"><?php _e('Durum', 'role-custom'); ?></th>
                                    <th scope="col"><?php _e('Mesaj', 'role-custom'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tests as $test): ?>
                                    <tr>
                                        <td><?php echo esc_html($test['test']); ?></td>
                                        <td>
                                            <span class="test-status <?php echo $test['success'] ? 'success' : 'error'; ?>">
                                                <?php echo $test['success'] ? '✓ Başarılı' : '✗ Başarısız'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo esc_html($test['message']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Performans Raporu -->
        <div class="performance-report">
            <h2><?php _e('Performans Raporu', 'role-custom'); ?></h2>
            
            <div class="performance-metrics">
                <div class="metric-card">
                    <div class="metric-value"><?php echo $performance_report['execution_time']; ?> ms</div>
                    <div class="metric-label"><?php _e('Çalışma Süresi', 'role-custom'); ?></div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value"><?php echo $performance_report['memory_usage']; ?> KB</div>
                    <div class="metric-label"><?php _e('Bellek Kullanımı', 'role-custom'); ?></div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value"><?php echo $performance_report['peak_memory']; ?> MB</div>
                    <div class="metric-label"><?php _e('Maksimum Bellek', 'role-custom'); ?></div>
                </div>
            </div>
            
            <div class="performance-analysis">
                <h4><?php _e('Performans Analizi', 'role-custom'); ?></h4>
                <ul>
                    <?php if ($performance_report['execution_time'] < 1000): ?>
                        <li class="analysis-good">✓ <?php _e('Çalışma süresi optimal (< 1 saniye)', 'role-custom'); ?></li>
                    <?php elseif ($performance_report['execution_time'] < 3000): ?>
                        <li class="analysis-warning">⚠ <?php _e('Çalışma süresi kabul edilebilir (1-3 saniye)', 'role-custom'); ?></li>
                    <?php else: ?>
                        <li class="analysis-bad">✗ <?php _e('Çalışma süresi yavaş (> 3 saniye)', 'role-custom'); ?></li>
                    <?php endif; ?>
                    
                    <?php if ($performance_report['memory_usage'] < 1024): ?>
                        <li class="analysis-good">✓ <?php _e('Bellek kullanımı düşük (< 1 MB)', 'role-custom'); ?></li>
                    <?php elseif ($performance_report['memory_usage'] < 5120): ?>
                        <li class="analysis-warning">⚠ <?php _e('Bellek kullanımı orta (1-5 MB)', 'role-custom'); ?></li>
                    <?php else: ?>
                        <li class="analysis-bad">✗ <?php _e('Bellek kullanımı yüksek (> 5 MB)', 'role-custom'); ?></li>
                    <?php endif; ?>
                    
                    <?php if ($performance_report['peak_memory'] < 50): ?>
                        <li class="analysis-good">✓ <?php _e('Maksimum bellek kullanımı düşük (< 50 MB)', 'role-custom'); ?></li>
                    <?php elseif ($performance_report['peak_memory'] < 100): ?>
                        <li class="analysis-warning">⚠ <?php _e('Maksimum bellek kullanımı orta (50-100 MB)', 'role-custom'); ?></li>
                    <?php else: ?>
                        <li class="analysis-bad">✗ <?php _e('Maksimum bellek kullanımı yüksek (> 100 MB)', 'role-custom'); ?></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
        
        <!-- Sistem Bilgileri -->
        <div class="system-info">
            <h2><?php _e('Sistem Bilgileri', 'role-custom'); ?></h2>
            
            <table class="wp-list-table widefat fixed striped">
                <tbody>
                    <tr>
                        <th scope="row"><?php _e('WordPress Sürümü', 'role-custom'); ?></th>
                        <td><?php echo get_bloginfo('version'); ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('PHP Sürümü', 'role-custom'); ?></th>
                        <td><?php echo PHP_VERSION; ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('MySQL Sürümü', 'role-custom'); ?></th>
                        <td><?php echo $GLOBALS['wpdb']->db_version(); ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Role Custom Sürümü', 'role-custom'); ?></th>
                        <td><?php echo ROLE_CUSTOM_VERSION; ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('WooCommerce', 'role-custom'); ?></th>
                        <td><?php echo class_exists('WooCommerce') ? 'Aktif (' . WC()->version . ')' : 'Pasif'; ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Tutor LMS', 'role-custom'); ?></th>
                        <td><?php echo function_exists('tutor') ? 'Aktif' : 'Pasif'; ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Bellek Limiti', 'role-custom'); ?></th>
                        <td><?php echo ini_get('memory_limit'); ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Maksimum Çalışma Süresi', 'role-custom'); ?></th>
                        <td><?php echo ini_get('max_execution_time'); ?> saniye</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Oneriler -->
        <div class="recommendations">
            <h2><?php _e('Öneriler', 'role-custom'); ?></h2>
            
            <div class="recommendation-list">
                <?php if ($test_report['summary']['failed'] > 0): ?>
                    <div class="recommendation error">
                        <h4><?php _e('Kritik Sorunlar', 'role-custom'); ?></h4>
                        <p><?php _e('Bazı testler başarısız oldu. Lütfen başarısız testleri kontrol edin ve sorunları çözün.', 'role-custom'); ?></p>
                    </div>
                <?php endif; ?>
                
                <?php if ($performance_report['execution_time'] > 3000): ?>
                    <div class="recommendation warning">
                        <h4><?php _e('Performans Uyarısı', 'role-custom'); ?></h4>
                        <p><?php _e('Sistem yavaş çalışıyor. Veritabanı optimizasyonu veya sunucu kaynaklarını artırma düşünün.', 'role-custom'); ?></p>
                    </div>
                <?php endif; ?>
                
                <?php if ($test_report['summary']['success_rate'] >= 90): ?>
                    <div class="recommendation success">
                        <h4><?php _e('Sistem Sağlıklı', 'role-custom'); ?></h4>
                        <p><?php _e('Para çekme sistemi düzgün çalışıyor. Düzenli olarak testleri çalıştırmaya devam edin.', 'role-custom'); ?></p>
                    </div>
                <?php endif; ?>
                
                <div class="recommendation info">
                    <h4><?php _e('Bakım Önerileri', 'role-custom'); ?></h4>
                    <ul>
                        <li><?php _e('Düzenli olarak veritabanı yedeklemesi alın', 'role-custom'); ?></li>
                        <li><?php _e('Güvenlik loglarını kontrol edin', 'role-custom'); ?></li>
                        <li><?php _e('Eklenti güncellemelerini takip edin', 'role-custom'); ?></li>
                        <li><?php _e('Performans metriklerini izleyin', 'role-custom'); ?></li>
                    </ul>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Test Bilgileri -->
        <div class="test-info">
            <h2><?php _e('Test Hakkında', 'role-custom'); ?></h2>
            
            <p><?php _e('Bu test paketi aşağıdaki bileşenleri kontrol eder:', 'role-custom'); ?></p>
            
            <ul>
                <li><strong><?php _e('Veritabanı Testleri:', 'role-custom'); ?></strong> <?php _e('Gerekli tabloların varlığını kontrol eder', 'role-custom'); ?></li>
                <li><strong><?php _e('Sınıf Testleri:', 'role-custom'); ?></strong> <?php _e('PHP sınıflarının doğru yüklendiğini kontrol eder', 'role-custom'); ?></li>
                <li><strong><?php _e('Fonksiyonel Testleri:', 'role-custom'); ?></strong> <?php _e('Para çekme işlemlerinin çalıştığını test eder', 'role-custom'); ?></li>
                <li><strong><?php _e('Güvenlik Testleri:', 'role-custom'); ?></strong> <?php _e('Güvenlik özelliklerinin aktif olduğunu kontrol eder', 'role-custom'); ?></li>
                <li><strong><?php _e('Bakiye Testleri:', 'role-custom'); ?></strong> <?php _e('Bakiye yönetim sistemini test eder', 'role-custom'); ?></li>
            </ul>
            
            <div class="test-warning">
                <p><strong><?php _e('Uyarı:', 'role-custom'); ?></strong> <?php _e('Testler sırasında geçici test verileri oluşturulur ve sonrasında silinir. Bu işlem birkaç dakika sürebilir.', 'role-custom'); ?></p>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.test-controls {
    margin: 20px 0;
    padding: 20px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.summary-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    border-left: 4px solid #2271b1;
}

.summary-card.success {
    border-left-color: #00a32a;
}

.summary-card.warning {
    border-left-color: #dba617;
}

.summary-card.error {
    border-left-color: #d63638;
}

.card-number {
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.card-label {
    color: #646970;
    font-size: 14px;
}

.test-status.success {
    color: #00a32a;
    font-weight: bold;
}

.test-status.error {
    color: #d63638;
    font-weight: bold;
}

.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.metric-card {
    background: #f6f7f7;
    padding: 15px;
    border-radius: 4px;
    text-align: center;
}

.metric-value {
    font-size: 1.5em;
    font-weight: bold;
    color: #2271b1;
}

.metric-label {
    color: #646970;
    font-size: 12px;
    margin-top: 5px;
}

.analysis-good {
    color: #00a32a;
}

.analysis-warning {
    color: #dba617;
}

.analysis-bad {
    color: #d63638;
}

.recommendation {
    margin: 15px 0;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid;
}

.recommendation.success {
    background: #f0f8ff;
    border-left-color: #00a32a;
}

.recommendation.warning {
    background: #fcf9e8;
    border-left-color: #dba617;
}

.recommendation.error {
    background: #fef7f7;
    border-left-color: #d63638;
}

.recommendation.info {
    background: #f0f6fc;
    border-left-color: #2271b1;
}

.test-warning {
    background: #fcf9e8;
    border: 1px solid #dba617;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}
</style>
