<?php
/**
 * Eğitmen Bakiye Durumu Sayfası
 * 
 * @package Role_Custom
 * @subpackage Templates
 * @since 1.4.0
 */

// <PERSON>rudan erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Mevcut kullaniciyi al
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Bakiye yoneticisini al
$balance_manager = Role_Custom_Balance_Manager::get_instance();
$withdraw_manager = Role_Custom_Withdraw_Manager::get_instance();

// Mevcut bakiyeyi al
$current_balance = $balance_manager->get_instructor_balance($user_id);

// Bakiye gecmisini al
$balance_history = $balance_manager->get_balance_history($user_id, 20);

// Bekleyen para çekme taleplerini al
$pending_withdraws = $withdraw_manager->get_user_withdraw_history($user_id, 10, 0, Role_Custom_Withdraw_Manager::STATUS_PENDING);

// Toplam kazanç ve çekilen tutarları hesapla
$total_earnings = 0;
$total_withdrawals = 0;

foreach ($balance_history as $transaction) {
    if ($transaction->debit > 0) {
        $total_earnings += $transaction->debit;
    }
    if ($transaction->credit > 0 && $transaction->transaction_type === 'withdraw') {
        $total_withdrawals += $transaction->credit;
    }
}

// Bekleyen çekim tutarını hesapla
$pending_withdrawal_amount = 0;
foreach ($pending_withdraws as $withdraw) {
    $pending_withdrawal_amount += $withdraw->amount;
}

?>

<div class="wrap instructor-balance-status">
    <h1 class="wp-heading-inline">
        <?php _e('Bakiye Durumu', 'role-custom'); ?>
    </h1>
    
    <!-- Bakiye Özeti -->
    <div class="balance-summary">
        <div class="summary-cards">
            <div class="summary-card current-balance">
                <div class="card-icon">💰</div>
                <div class="card-content">
                    <div class="card-amount"><?php echo wc_price($current_balance); ?></div>
                    <div class="card-label"><?php _e('Mevcut Bakiye', 'role-custom'); ?></div>
                </div>
            </div>
            
            <div class="summary-card total-earnings">
                <div class="card-icon">📈</div>
                <div class="card-content">
                    <div class="card-amount"><?php echo wc_price($total_earnings); ?></div>
                    <div class="card-label"><?php _e('Toplam Kazanç', 'role-custom'); ?></div>
                </div>
            </div>
            
            <div class="summary-card total-withdrawals">
                <div class="card-icon">💸</div>
                <div class="card-content">
                    <div class="card-amount"><?php echo wc_price($total_withdrawals); ?></div>
                    <div class="card-label"><?php _e('Çekilen Tutar', 'role-custom'); ?></div>
                </div>
            </div>
            
            <div class="summary-card pending-withdrawals">
                <div class="card-icon">⏳</div>
                <div class="card-content">
                    <div class="card-amount"><?php echo wc_price($pending_withdrawal_amount); ?></div>
                    <div class="card-label"><?php _e('Bekleyen Çekim', 'role-custom'); ?></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Hızlı İşlemler -->
    <div class="quick-actions">
        <h2><?php _e('Hızlı İşlemler', 'role-custom'); ?></h2>
        <div class="action-buttons">
            <a href="<?php echo admin_url('admin.php?page=instructor-withdraw'); ?>" class="button button-primary button-large">
                <?php _e('Para Çek', 'role-custom'); ?>
            </a>
            <a href="<?php echo admin_url('admin.php?page=instructor-withdraw-history'); ?>" class="button button-secondary">
                <?php _e('Talep Geçmişi', 'role-custom'); ?>
            </a>
        </div>
    </div>
    
    <!-- Bekleyen Talepler -->
    <?php if (!empty($pending_withdraws)): ?>
        <div class="pending-withdraws-section">
            <h2><?php _e('Bekleyen Para Çekme Talepleri', 'role-custom'); ?></h2>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col"><?php _e('Talep ID', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Tarih', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Tutar', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Yöntem', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('İşlemler', 'role-custom'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pending_withdraws as $withdraw): ?>
                        <tr>
                            <td><strong>#<?php echo $withdraw->id; ?></strong></td>
                            <td><?php echo date_i18n('d.m.Y H:i', strtotime($withdraw->created_at)); ?></td>
                            <td><?php echo wc_price($withdraw->amount); ?></td>
                            <td>
                                <?php 
                                $methods = [
                                    'bank' => __('Banka Transferi', 'role-custom'),
                                    'paypal' => __('PayPal', 'role-custom')
                                ];
                                echo $methods[$withdraw->method] ?? $withdraw->method;
                                ?>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=instructor-withdraw-history&view=' . $withdraw->id); ?>" class="button button-small">
                                    <?php _e('Detay', 'role-custom'); ?>
                                </a>
                                <button type="button" class="button button-small cancel-withdraw" data-id="<?php echo $withdraw->id; ?>">
                                    <?php _e('İptal', 'role-custom'); ?>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
    
    <!-- Bakiye Geçmişi -->
    <div class="balance-history-section">
        <h2><?php _e('Son Bakiye Hareketleri', 'role-custom'); ?></h2>
        
        <?php if (empty($balance_history)): ?>
            <div class="no-transactions">
                <p><?php _e('Henüz hiç bakiye hareketi bulunmuyor.', 'role-custom'); ?></p>
            </div>
        <?php else: ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col"><?php _e('Tarih', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('İşlem Tipi', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Açıklama', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Gelen', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Giden', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Bakiye', 'role-custom'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($balance_history as $transaction): ?>
                        <tr>
                            <td><?php echo date_i18n('d.m.Y H:i', strtotime($transaction->created_at)); ?></td>
                            <td>
                                <?php
                                $transaction_types = [
                                    'course_sale' => __('Kurs Satışı', 'role-custom'),
                                    'product_sale' => __('Ürün Satışı', 'role-custom'),
                                    'withdraw' => __('Para Çekme', 'role-custom'),
                                    'refund' => __('İade', 'role-custom'),
                                    'manual' => __('Manuel İşlem', 'role-custom')
                                ];
                                echo $transaction_types[$transaction->transaction_type] ?? $transaction->transaction_type;
                                ?>
                            </td>
                            <td><?php echo esc_html($transaction->description); ?></td>
                            <td>
                                <?php if ($transaction->debit > 0): ?>
                                    <span class="amount-positive">+<?php echo wc_price($transaction->debit); ?></span>
                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($transaction->credit > 0): ?>
                                    <span class="amount-negative">-<?php echo wc_price($transaction->credit); ?></span>
                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </td>
                            <td><strong><?php echo wc_price($transaction->balance); ?></strong></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <p class="view-all">
                <a href="<?php echo admin_url('admin.php?page=instructor-withdraw-history'); ?>" class="button">
                    <?php _e('Tüm Geçmişi Görüntüle', 'role-custom'); ?>
                </a>
            </p>
        <?php endif; ?>
    </div>
    
    <!-- Bilgilendirme -->
    <div class="balance-info">
        <h2><?php _e('Bakiye Hakkında Bilgiler', 'role-custom'); ?></h2>
        
        <div class="info-grid">
            <div class="info-card">
                <h3><?php _e('Bakiye Nasıl Oluşur?', 'role-custom'); ?></h3>
                <ul>
                    <li><?php _e('Kurs satışlarından komisyon', 'role-custom'); ?></li>
                    <li><?php _e('Ürün satışlarından komisyon', 'role-custom'); ?></li>
                    <li><?php _e('Manuel bakiye eklemeleri', 'role-custom'); ?></li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3><?php _e('Para Çekme Kuralları', 'role-custom'); ?></h3>
                <ul>
                    <li><?php _e('Minimum çekim tutarı: 50 TL', 'role-custom'); ?></li>
                    <li><?php _e('Günde maksimum 3 talep', 'role-custom'); ?></li>
                    <li><?php _e('İşlem ücreti ödeme yöntemine göre değişir', 'role-custom'); ?></li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3><?php _e('İşlem Süreleri', 'role-custom'); ?></h3>
                <ul>
                    <li><?php _e('Banka transferi: 1-3 iş günü', 'role-custom'); ?></li>
                    <li><?php _e('PayPal: 24 saat içinde', 'role-custom'); ?></li>
                    <li><?php _e('Talepler 24 saat içinde değerlendirilir', 'role-custom'); ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.instructor-balance-status {
    max-width: 1200px;
}

.balance-summary {
    margin: 20px 0;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.summary-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: box-shadow 0.3s ease;
}

.summary-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.summary-card.current-balance {
    border-left: 4px solid #00a32a;
}

.summary-card.total-earnings {
    border-left: 4px solid #2271b1;
}

.summary-card.total-withdrawals {
    border-left: 4px solid #dba617;
}

.summary-card.pending-withdrawals {
    border-left: 4px solid #d63638;
}

.card-icon {
    font-size: 2em;
}

.card-amount {
    font-size: 1.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.card-label {
    color: #646970;
    font-size: 14px;
}

.quick-actions {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.pending-withdraws-section,
.balance-history-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.no-transactions {
    text-align: center;
    padding: 40px;
    color: #646970;
}

.amount-positive {
    color: #00a32a;
    font-weight: bold;
}

.amount-negative {
    color: #d63638;
    font-weight: bold;
}

.balance-info {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.info-card {
    background: #f6f7f7;
    border-radius: 4px;
    padding: 15px;
}

.info-card h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #2271b1;
}

.info-card ul {
    margin: 0;
    padding-left: 20px;
}

.info-card li {
    margin-bottom: 5px;
}

.view-all {
    text-align: center;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Talep iptal etme
    $('.cancel-withdraw').click(function() {
        var withdrawId = $(this).data('id');
        
        if (confirm('Bu talebi iptal etmek istediğinizden emin misiniz?')) {
            $.post(ajaxurl, {
                action: 'role_custom_cancel_withdraw_request',
                withdraw_id: withdrawId,
                nonce: '<?php echo wp_create_nonce("cancel_withdraw"); ?>'
            }, function(response) {
                if (response.success) {
                    alert('Talep başarıyla iptal edildi.');
                    location.reload();
                } else {
                    alert('Hata: ' + response.data);
                }
            });
        }
    });
});
</script>
