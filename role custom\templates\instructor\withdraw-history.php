<?php
/**
 * Eğitmen Para Çekme Talep Geçmişi Sayfası
 * 
 * @package Role_Custom
 * @subpackage Templates
 * @since 1.4.0
 */

// <PERSON><PERSON>an erisimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Mevcut kullaniciyi al
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Withdraw manager'i al
$withdraw_manager = Role_Custom_Withdraw_Manager::get_instance();

// Sayfalama parametreleri
$per_page = 20;
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$offset = ($current_page - 1) * $per_page;

// Filtre parametreleri
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$method_filter = isset($_GET['method']) ? sanitize_text_field($_GET['method']) : '';

// Talep geçmişini al
$withdraws = $withdraw_manager->get_user_withdraw_history($user_id, $per_page, $offset, $status_filter, $method_filter);

// Toplam talep sayısını al
$total_withdraws = $withdraw_manager->get_user_withdraw_count($user_id, $status_filter, $method_filter);

// Sayfa sayısını hesapla
$total_pages = ceil($total_withdraws / $per_page);

// Detay görüntüleme
$view_withdraw_id = isset($_GET['view']) ? intval($_GET['view']) : 0;
$withdraw_detail = null;

if ($view_withdraw_id) {
    $withdraw_detail = $withdraw_manager->get_withdraw_by_id($view_withdraw_id);
    // Kullanıcının kendi talebi olduğunu kontrol et
    if (!$withdraw_detail || $withdraw_detail->user_id != $user_id) {
        $withdraw_detail = null;
    }
}

?>

<div class="wrap instructor-withdraw-history">
    <h1 class="wp-heading-inline">
        <?php _e('Para Çekme Talep Geçmişi', 'role-custom'); ?>
    </h1>
    
    <a href="<?php echo admin_url('admin.php?page=instructor-withdraw'); ?>" class="page-title-action">
        <?php _e('Yeni Talep Oluştur', 'role-custom'); ?>
    </a>
    
    <?php if ($withdraw_detail): ?>
        <!-- Talep Detayı -->
        <div class="withdraw-detail-modal">
            <div class="detail-header">
                <h2><?php _e('Talep Detayı', 'role-custom'); ?> #<?php echo $withdraw_detail->id; ?></h2>
                <a href="<?php echo admin_url('admin.php?page=instructor-withdraw-history'); ?>" class="button">
                    <?php _e('Listeye Dön', 'role-custom'); ?>
                </a>
            </div>
            
            <div class="detail-content">
                <div class="detail-grid">
                    <div class="detail-section">
                        <h3><?php _e('Talep Bilgileri', 'role-custom'); ?></h3>
                        <table class="detail-table">
                            <tr>
                                <th><?php _e('Talep Tarihi:', 'role-custom'); ?></th>
                                <td><?php echo date_i18n('d.m.Y H:i', strtotime($withdraw_detail->created_at)); ?></td>
                            </tr>
                            <tr>
                                <th><?php _e('Talep Tutarı:', 'role-custom'); ?></th>
                                <td><?php echo wc_price($withdraw_detail->amount); ?></td>
                            </tr>
                            <tr>
                                <th><?php _e('İşlem Ücreti:', 'role-custom'); ?></th>
                                <td><?php echo wc_price($withdraw_detail->charge_amount); ?></td>
                            </tr>
                            <tr>
                                <th><?php _e('Alacağınız Tutar:', 'role-custom'); ?></th>
                                <td><strong><?php echo wc_price($withdraw_detail->receivable_amount); ?></strong></td>
                            </tr>
                            <tr>
                                <th><?php _e('Ödeme Yöntemi:', 'role-custom'); ?></th>
                                <td>
                                    <?php 
                                    $methods = [
                                        'bank' => __('Banka Transferi', 'role-custom'),
                                        'paypal' => __('PayPal', 'role-custom')
                                    ];
                                    echo $methods[$withdraw_detail->method] ?? $withdraw_detail->method;
                                    ?>
                                </td>
                            </tr>
                            <tr>
                                <th><?php _e('Durum:', 'role-custom'); ?></th>
                                <td>
                                    <?php
                                    $statuses = [
                                        Role_Custom_Withdraw_Manager::STATUS_PENDING => ['label' => __('Bekliyor', 'role-custom'), 'class' => 'pending'],
                                        Role_Custom_Withdraw_Manager::STATUS_APPROVED => ['label' => __('Onaylandı', 'role-custom'), 'class' => 'approved'],
                                        Role_Custom_Withdraw_Manager::STATUS_REJECTED => ['label' => __('Reddedildi', 'role-custom'), 'class' => 'rejected'],
                                        Role_Custom_Withdraw_Manager::STATUS_CANCELLED => ['label' => __('İptal Edildi', 'role-custom'), 'class' => 'cancelled'],
                                        Role_Custom_Withdraw_Manager::STATUS_COMPLETED => ['label' => __('Tamamlandı', 'role-custom'), 'class' => 'completed']
                                    ];
                                    $status = $statuses[$withdraw_detail->status] ?? ['label' => $withdraw_detail->status, 'class' => 'unknown'];
                                    ?>
                                    <span class="status-badge status-<?php echo $status['class']; ?>">
                                        <?php echo $status['label']; ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="detail-section">
                        <h3><?php _e('Ödeme Detayları', 'role-custom'); ?></h3>
                        <?php 
                        $payment_details = maybe_unserialize($withdraw_detail->payment_details);
                        if ($payment_details && is_array($payment_details)):
                        ?>
                            <table class="detail-table">
                                <?php if ($withdraw_detail->method === 'bank'): ?>
                                    <tr>
                                        <th><?php _e('Banka Adı:', 'role-custom'); ?></th>
                                        <td><?php echo esc_html($payment_details['bank_name'] ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th><?php _e('Hesap Sahibi:', 'role-custom'); ?></th>
                                        <td><?php echo esc_html($payment_details['account_holder'] ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th><?php _e('IBAN:', 'role-custom'); ?></th>
                                        <td><?php echo esc_html($payment_details['iban'] ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th><?php _e('Hesap No:', 'role-custom'); ?></th>
                                        <td><?php echo esc_html($payment_details['account_number'] ?? '-'); ?></td>
                                    </tr>
                                <?php elseif ($withdraw_detail->method === 'paypal'): ?>
                                    <tr>
                                        <th><?php _e('PayPal E-posta:', 'role-custom'); ?></th>
                                        <td><?php echo esc_html($payment_details['email'] ?? '-'); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        <?php else: ?>
                            <p><?php _e('Ödeme detayı bulunamadı.', 'role-custom'); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if ($withdraw_detail->note): ?>
                    <div class="detail-section">
                        <h3><?php _e('Not', 'role-custom'); ?></h3>
                        <p><?php echo esc_html($withdraw_detail->note); ?></p>
                    </div>
                <?php endif; ?>
                
                <?php if ($withdraw_detail->admin_note): ?>
                    <div class="detail-section">
                        <h3><?php _e('Admin Notu', 'role-custom'); ?></h3>
                        <p><?php echo esc_html($withdraw_detail->admin_note); ?></p>
                    </div>
                <?php endif; ?>
                
                <?php if ($withdraw_detail->status == Role_Custom_Withdraw_Manager::STATUS_PENDING): ?>
                    <div class="detail-actions">
                        <button type="button" class="button button-secondary cancel-withdraw" data-id="<?php echo $withdraw_detail->id; ?>">
                            <?php _e('Talebi İptal Et', 'role-custom'); ?>
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Filtreler -->
        <div class="withdraw-filters">
            <form method="get" action="">
                <input type="hidden" name="page" value="instructor-withdraw-history">
                
                <label for="status-filter"><?php _e('Durum:', 'role-custom'); ?></label>
                <select name="status" id="status-filter">
                    <option value=""><?php _e('Tüm Durumlar', 'role-custom'); ?></option>
                    <option value="<?php echo Role_Custom_Withdraw_Manager::STATUS_PENDING; ?>" <?php selected($status_filter, Role_Custom_Withdraw_Manager::STATUS_PENDING); ?>><?php _e('Bekliyor', 'role-custom'); ?></option>
                    <option value="<?php echo Role_Custom_Withdraw_Manager::STATUS_APPROVED; ?>" <?php selected($status_filter, Role_Custom_Withdraw_Manager::STATUS_APPROVED); ?>><?php _e('Onaylandı', 'role-custom'); ?></option>
                    <option value="<?php echo Role_Custom_Withdraw_Manager::STATUS_REJECTED; ?>" <?php selected($status_filter, Role_Custom_Withdraw_Manager::STATUS_REJECTED); ?>><?php _e('Reddedildi', 'role-custom'); ?></option>
                    <option value="<?php echo Role_Custom_Withdraw_Manager::STATUS_CANCELLED; ?>" <?php selected($status_filter, Role_Custom_Withdraw_Manager::STATUS_CANCELLED); ?>><?php _e('İptal Edildi', 'role-custom'); ?></option>
                    <option value="<?php echo Role_Custom_Withdraw_Manager::STATUS_COMPLETED; ?>" <?php selected($status_filter, Role_Custom_Withdraw_Manager::STATUS_COMPLETED); ?>><?php _e('Tamamlandı', 'role-custom'); ?></option>
                </select>
                
                <label for="method-filter"><?php _e('Yöntem:', 'role-custom'); ?></label>
                <select name="method" id="method-filter">
                    <option value=""><?php _e('Tüm Yöntemler', 'role-custom'); ?></option>
                    <option value="bank" <?php selected($method_filter, 'bank'); ?>><?php _e('Banka Transferi', 'role-custom'); ?></option>
                    <option value="paypal" <?php selected($method_filter, 'paypal'); ?>><?php _e('PayPal', 'role-custom'); ?></option>
                </select>
                
                <input type="submit" class="button" value="<?php _e('Filtrele', 'role-custom'); ?>">
                <a href="<?php echo admin_url('admin.php?page=instructor-withdraw-history'); ?>" class="button"><?php _e('Temizle', 'role-custom'); ?></a>
            </form>
        </div>
        
        <!-- Talep Listesi -->
        <?php if (empty($withdraws)): ?>
            <div class="no-withdraws">
                <p><?php _e('Henüz hiç para çekme talebiniz bulunmuyor.', 'role-custom'); ?></p>
                <a href="<?php echo admin_url('admin.php?page=instructor-withdraw'); ?>" class="button button-primary">
                    <?php _e('İlk Talebinizi Oluşturun', 'role-custom'); ?>
                </a>
            </div>
        <?php else: ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col"><?php _e('ID', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Tarih', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Tutar', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Alacak', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Yöntem', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('Durum', 'role-custom'); ?></th>
                        <th scope="col"><?php _e('İşlemler', 'role-custom'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($withdraws as $withdraw): ?>
                        <tr>
                            <td><strong>#<?php echo $withdraw->id; ?></strong></td>
                            <td><?php echo date_i18n('d.m.Y H:i', strtotime($withdraw->created_at)); ?></td>
                            <td><?php echo wc_price($withdraw->amount); ?></td>
                            <td><strong><?php echo wc_price($withdraw->receivable_amount); ?></strong></td>
                            <td>
                                <?php 
                                $methods = [
                                    'bank' => __('Banka', 'role-custom'),
                                    'paypal' => __('PayPal', 'role-custom')
                                ];
                                echo $methods[$withdraw->method] ?? $withdraw->method;
                                ?>
                            </td>
                            <td>
                                <?php
                                $statuses = [
                                    Role_Custom_Withdraw_Manager::STATUS_PENDING => ['label' => __('Bekliyor', 'role-custom'), 'class' => 'pending'],
                                    Role_Custom_Withdraw_Manager::STATUS_APPROVED => ['label' => __('Onaylandı', 'role-custom'), 'class' => 'approved'],
                                    Role_Custom_Withdraw_Manager::STATUS_REJECTED => ['label' => __('Reddedildi', 'role-custom'), 'class' => 'rejected'],
                                    Role_Custom_Withdraw_Manager::STATUS_CANCELLED => ['label' => __('İptal', 'role-custom'), 'class' => 'cancelled'],
                                    Role_Custom_Withdraw_Manager::STATUS_COMPLETED => ['label' => __('Tamamlandı', 'role-custom'), 'class' => 'completed']
                                ];
                                $status = $statuses[$withdraw->status] ?? ['label' => $withdraw->status, 'class' => 'unknown'];
                                ?>
                                <span class="status-badge status-<?php echo $status['class']; ?>">
                                    <?php echo $status['label']; ?>
                                </span>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=instructor-withdraw-history&view=' . $withdraw->id); ?>" class="button button-small">
                                    <?php _e('Detay', 'role-custom'); ?>
                                </a>
                                <?php if ($withdraw->status == Role_Custom_Withdraw_Manager::STATUS_PENDING): ?>
                                    <button type="button" class="button button-small cancel-withdraw" data-id="<?php echo $withdraw->id; ?>">
                                        <?php _e('İptal', 'role-custom'); ?>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Sayfalama -->
            <?php if ($total_pages > 1): ?>
                <div class="tablenav">
                    <div class="tablenav-pages">
                        <?php
                        $pagination_args = [
                            'base' => add_query_arg('paged', '%#%'),
                            'format' => '',
                            'prev_text' => __('&laquo;'),
                            'next_text' => __('&raquo;'),
                            'total' => $total_pages,
                            'current' => $current_page
                        ];
                        echo paginate_links($pagination_args);
                        ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
.instructor-withdraw-history {
    max-width: 1200px;
}

.withdraw-filters {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.withdraw-filters form {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.withdraw-filters label {
    font-weight: bold;
}

.withdraw-detail-modal {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin: 20px 0;
}

.detail-header {
    background: #f6f7f7;
    border-bottom: 1px solid #c3c4c7;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-content {
    padding: 20px;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 20px;
}

.detail-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #2271b1;
}

.detail-table {
    width: 100%;
    border-collapse: collapse;
}

.detail-table th,
.detail-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f1;
    text-align: left;
}

.detail-table th {
    font-weight: bold;
    width: 40%;
}

.detail-actions {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #c3c4c7;
}

.no-withdraws {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 40px;
    text-align: center;
    margin: 20px 0;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background: #fcf9e8;
    color: #dba617;
}

.status-approved {
    background: #f0f8ff;
    color: #2271b1;
}

.status-rejected {
    background: #fef7f7;
    color: #d63638;
}

.status-cancelled {
    background: #f6f7f7;
    color: #646970;
}

.status-completed {
    background: #f0f8f0;
    color: #00a32a;
}

@media (max-width: 768px) {
    .detail-grid {
        grid-template-columns: 1fr;
    }
    
    .withdraw-filters form {
        flex-direction: column;
        align-items: stretch;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Talep iptal etme
    $('.cancel-withdraw').click(function() {
        var withdrawId = $(this).data('id');
        
        if (confirm('Bu talebi iptal etmek istediğinizden emin misiniz?')) {
            $.post(ajaxurl, {
                action: 'role_custom_cancel_withdraw_request',
                withdraw_id: withdrawId,
                nonce: '<?php echo wp_create_nonce("cancel_withdraw"); ?>'
            }, function(response) {
                if (response.success) {
                    alert('Talep başarıyla iptal edildi.');
                    location.reload();
                } else {
                    alert('Hata: ' + response.data);
                }
            });
        }
    });
});
</script>
